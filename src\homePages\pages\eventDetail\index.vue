<script setup>
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'

const systemInfo = uni.getSystemInfoSync()
const safeAreaInsets = systemInfo.safeAreaInsets || {} // 可能为 undefined
const topSafeHeight = safeAreaInsets.top || 0 // 顶部安全高度
const safeHeight = 1
</script>

<template>
  <SafeTopArea>
    <List>
      <SafeArea class="flex flex-col font-[PingFangSC]">
        <view class="relative h-[180px] w-full bg-gradient-to-r from-[#66D0CA] to-[#79DAB7]">
          <Header title="活动详情" />
          <view
            class="absolute w-screen bg-gradient-to-r from-[#66D0CA] to-[#79DAB7]"
            :style="{ height: `${topSafeHeight + safeHeight}px`, top: `-${topSafeHeight - safeHeight}px` }"
          />
        </view>
        <!-- 盖住部分 Header -->
        <scroll-view scroll-y class="z-10 mt-[-100px] h-[calc(100%-70px)]  bg-[#f8f8f8] pb-[40px]">
          <view class="flex flex-col gap-[10px] pb-[40px]">
            <view class="m-auto w-[95%] rounded-[5px] bg-white p-[14.5px]">
              <text class="text-[15px] font-[500] leading-[22.5px] text-[#202027]">
                国网江苏省电力有限公司2025年高校毕业生招聘考试资格审核活动
              </text>
              <view
                class="mt-[5px] flex flex-col  gap-[10px] border-t pt-[10px] text-[13px] font-[400] leading-[23px] text-[#63676F]"
              >
                <text>审核地点：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  国网江苏省电力有限公司技能培训中心（江苏省苏州市姑苏区劳动路599号）
                </text>
                <text>资格审核时间：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  2025-05-11 8:00-18:00
                </text>
                <text>活动说明：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  请提前10分钟到达技培中心门口，等待进入
                </text>
              </view>
            </view>

            <view class="m-auto w-[95%] rounded-[5px] bg-white p-[14.5px]">
              <view class=" flex flex-col gap-[10px] text-[14px] font-[400] leading-[27px] text-[#63676F]">
                <text>考生姓名：<text class="text-[#1D1D20]">韩梅梅</text></text>
                <text>身份证号：<text class="text-[#1D1D20]">320681200302267010</text></text>
                <text>手机号码：<text class="text-[#1D1D20]">18951326690</text></text>
                <text>考试专业：<text class="text-[#1D1D20]">电工类</text></text>
                <text>应聘单位：<text class="text-[#1D1D20]">国网江苏省电力有限公司</text></text>
              </view>
            </view>
          </view>
        </scroll-view>
      </SafeArea>
    </List>
  </SafeTopArea>
</template>

<style scoped></style>
