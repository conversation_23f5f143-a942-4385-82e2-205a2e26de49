<script setup>
const props = defineProps({
  header: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  tabbar: {
    type: <PERSON>olean,
    default: false,
  },
  acknowledge: {
    type: Boolean,
    default: false,
  },
  bottomBtn: {
    type: Boolean,
    default: false,
  },
})
// console.log(props.header, props.tabbar);

const cancelHeight = (props.header ? 130 : 0) + (props.tabbar ? 100 : 0) + (props.acknowledge ? 200 : 0) + (props.bottomBtn ? 80 : 0)
// console.log(cancelHeight);

const systemInfo = uni.getSystemInfoSync()
const safeAreaInsets = systemInfo.safeAreaInsets || {} // 可能为 undefined
const bottomSafeHeight = safeAreaInsets.bottom || 0 // 底部安全高度
const topSafeHeight = safeAreaInsets.top || 0 // 顶部安全高度
// console.log(bottomSafeHeight, topSafeHeight)
const heightStyle = `calc(100vh - ${cancelHeight}rpx - ${topSafeHeight}px - ${bottomSafeHeight}px)`
</script>

<template>
  <view :style="{ height: heightStyle }">
    <slot />
  </view>
</template>

<style></style>
