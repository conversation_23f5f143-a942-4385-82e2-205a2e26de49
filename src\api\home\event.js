// 首页--活动
import request from '@/utils/request'

// 获取活动列表
export function getEventList(params) {
  return request.get('/app/event/list', params)
}

// 获取我的活动列表
export function getMyEventList(params) {
  return request.get('/app/event/my-list', params)
}

// 获取预约时间段
export function getSubTime(params) {
  return request.get('/app/event-time-slot/list', params)
}

// 获取本次活动本人状态
export function getEventMineStatus(params) {
  return request.get('/app/student/my-info', params)
}

// 提交签名
export function postSign(params) {
  return request.get('/app/student/generateStudentSignFile', params)
}

// 获取本人状态
export function getMineStatus(params) {
  return request.get('/app/student/my-status', params)
}

// 预约场次
export function postSubscribe(params) {
  /**
   * eventId:活动Id
   * timeSlotId：预约时段Id
   * rest：其他字段
   */
  const { eventId, timeSlotId, ...rest } = params
  return request.post(`/app/process/appointment/appoint?eventId=${params.eventId}&timeSlotId=${params.timeSlotId}`, rest)
}
