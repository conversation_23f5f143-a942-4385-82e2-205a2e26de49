import Mock from 'mockjs'

// 考生审核状态
export const Statistics = [
  {
    title: '已分配',
    num: 22,
  },
  {
    title: '已审定',
    num: 12,
  },
  {
    title: '队列中',
    num: 7,
  },
]

// 审核人员数据
export const auditPerson = Mock.mock({
  'list|10-20': [
    {
      'id|+1': 0,
      'name': '@ctitle(2,3)',
      'sex|1': [0, 1], // 0-女 1-男
      'type|1': [0, 1], // 0-境内 1-境外
      'uploadStatus|1': [0, 1], // 0-未上传 1-已上传
      'approveStatus': function () {
        if (this.uploadStatus === 0) {
          return 0 // 未审定
        }
        else {
          return Mock.Random.integer(0, 2) // 1-已通过 2-已拒绝
        }
      },
      'identity': /^[1-9]\d{5}(19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/i,
      'phone': /^1[3-9]\d{9}$/,
      'school': '@ctitle(4)',
      'specialty|1': ['电子信息', '软件工程', '土木工程', '机械工程'],
      'level|1': ['本科', '硕士', '博士'],
      'examType|1': ['电工类', '计算机类', '经济类', '医学类', '机械类'],
      'applyTime': '@datetime(yyyy-MM-dd HH:mm:ss)',
    },
  ],
}).list

// 审核人员内容对应名称数据
export const auditPersonTitle = [
  {
    id: 0,
    title: '身份证号',
    content: 'identity',
  },
  {
    id: 1,
    title: '电话号码',
    content: 'phone',
  },
  {
    id: 2,
    title: '院校',
    content: 'school',
  },
  {
    id: 3,
    title: '专业',
    content: 'specialty',
  },
  {
    id: 4,
    title: '考试层次',
    content: 'level',
  },
  {
    id: 5,
    title: '考试专业',
    content: 'examType',
  },
]

export const approveRes = ['通过', '不通过']

// 考生数据
export const studentArr = Mock.mock({
  'list|10-20': [{
    'id|+1': 0,
    'name': '@ctitle(2,3)',
  }],
}).list

// 专家数据
export const specialistArr = Mock.mock({
  'list|5-10': [{
    'id|+1': 0,
    'name': '@ctitle(2,3)',
  }],
}).list

// 专家操作数据
export const handleList = [
  {
    id: 0,
    title: '扫码绑定桌位',
    icon: 'scan',
  },
  {
    id: 1,
    title: '暂停分配考生',
    icon: 'stop',
  },
  {
    id: 2,
    title: '转移在队考生',
    icon: 'shift',
  },
]

// 转移考生操作
export const shiftText = [
  {
    id: 0,
    title: '考生分配已暂停，选择需转移的考生',
    name: 'selectStudent',
  },
  {
    id: 1,
    title: '选择当前场地内的在审专家',
    name: 'selectSpecialist',
  },
]
