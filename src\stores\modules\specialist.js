export const useSpecialistStore = defineStore('specialist', () => {
  const student = ref([])
  const specialist = ref({})
  function setStudent(data) {
    student.value = data
  }

  function clearStudent() {
    student.value = []
  }

  function setSpecialist(data) {
    specialist.value = data
  }

  function clearSpecialist() {
    specialist.value = {}
  }
  return {
    student,
    specialist,
    setStudent,
    clearStudent,
    setSpecialist,
    clearSpecialist,
  }
})
