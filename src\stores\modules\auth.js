export const useAuthStore = defineStore('auth', () => {
  const showLogin = ref(true)

  // 用户角色
  /**
   * 0--学生
   * 1--身份核验人员
   * 2--打印员
   * 3--专家
   */
  const userRoles = ref([0, 1, 2, 3])
  const role = ref()
  const id = ref()

  function setId(id) {
    id.value = id
  }

  function login() {
    showLogin.value = false
  }

  function logout() {
    showLogin.value = true
  }

  function chooseRole(item) {
    role.value = item
  }

  return {
    showLogin,
    userRoles,
    role,
    login,
    logout,
    chooseRole,
  }
})
