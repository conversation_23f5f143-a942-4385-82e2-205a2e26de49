// request.js

// -------------------- 配置区域 --------------------
// 1. 基础路径配置
const BASE_URL = import.meta.env.VITE_BASE_URL

// 2. 请求超时时间（毫秒）
const TIMEOUT = 15000 // 15秒

// 3. Token本地存储的Key
const TOKEN_KEY = 'user_token'

// 4. 未授权时跳转的登录页面路径
const LOGIN_PAGE_URL = '/pages/pageComponents/login' // 请根据您的项目路径修改

// 5. 默认请求头
const DEFAULT_HEADERS = {
  'Content-Type': 'application/json;charset=UTF-8',
  // 'X-Requested-With': 'XMLHttpRequest', // 如果后端需要，可以加上
}

// -------------------- 核心请求函数 --------------------
function request(options) {
  return new Promise((resolve, reject) => {
    // --- 1. 请求拦截器 ---
    const requestOptions = { ...options } // 复制一份，避免直接修改原options

    // 自动拼接baseURL
    requestOptions.url = BASE_URL + requestOptions.url

    // 设置默认请求方法（如果未提供）
    requestOptions.method = requestOptions.method ? requestOptions.method.toUpperCase() : 'GET'

    // 设置默认超时时间
    requestOptions.timeout = requestOptions.timeout || TIMEOUT

    // 合并默认请求头和自定义请求头
    requestOptions.header = {
      ...DEFAULT_HEADERS,
      ...(requestOptions.header || {}),
    }

    // 自动携带Token或用户信息
    const token = uni.getStorageSync(TOKEN_KEY)
    const clientId = uni.getStorageSync('id')
    if (token) {
      requestOptions.header.Authorization = `Bearer ${token}`
      requestOptions.header.Clientid = `${clientId}`
      // 如果还有其他用户信息需要携带，可以在这里添加
      // const userInfo = uni.getStorageSync('user_info');
      // if (userInfo) {
      //     requestOptions.header['X-User-Id'] = userInfo.id;
      // }
    }

    // --- 2. 发起uniapp请求 ---
    uni.request({
      ...requestOptions,
      success: (res) => {
        // --- 3. 响应拦截器 ---
        const statusCode = res.statusCode
        const responseData = res.data // 后端返回的原始数据

        if (statusCode >= 200 && statusCode < 300) {
          // 请求成功
          // 通常后端会定义一个业务状态码，比如 code: 0 或 code: 200 表示成功
          // 请根据您的后端API规范调整这里的判断
          if (responseData.code === 0 || responseData.code === 200 || typeof responseData.code === 'undefined') {
            // 业务成功
            resolve(responseData.data || responseData) // 返回data字段或整个responseData
          }
          else {
            // 业务失败 (例如：参数错误、库存不足等后端定义的错误)
            handleBusinessError(responseData, reject)
          }
        }
        else {
          // HTTP状态码错误
          handleHttpError(statusCode, responseData, reject)
        }
      },
      fail: (err) => {
        // --- 4. 网络错误或其他uni.request执行错误 ---
        handleNetworkError(err, reject)
      },
      complete: () => {
        // 可选：请求完成后的操作，例如关闭loading
        // uni.hideLoading();
      },
    })
  })
}

// -------------------- 错误处理机制 --------------------

/**
 * 处理业务逻辑错误 (HTTP状态码2xx，但后端返回的业务code表示失败)
 * @param {object} responseData 后端返回的完整数据
 * @param {Function} reject Promise的reject函数
 */
function handleBusinessError(responseData, reject) {
  const errMsg = responseData.message || responseData.msg || '业务处理失败，请稍后再试'
  uni.showToast({
    title: errMsg,
    icon: 'none',
    duration: 2000,
  })
  // console.error('[Business Error]:', responseData)
  reject({
    isBusinessError: true,
    code: responseData.code,
    message: errMsg,
    data: responseData,
  })
}

/**
 * 处理HTTP状态码错误 (非2xx)
 * @param {number} statusCode HTTP状态码
 * @param {object} responseData 后端返回的完整数据
 * @param {Function} reject Promise的reject函数
 */
function handleHttpError(statusCode, responseData, reject) {
  let errMsg = `请求错误 ${statusCode}`
  switch (statusCode) {
    case 400:
      errMsg = responseData.message || responseData.msg || '请求参数错误'
      break
    case 401:
      errMsg = '未授权或登录已过期，请重新登录'
      // 清除本地token和用户信息
      uni.removeStorageSync(TOKEN_KEY)
      // uni.removeStorageSync('user_info'); // 如果有其他用户信息
      // 跳转到登录页
      uni.navigateTo({
        url: LOGIN_PAGE_URL,
      })
      break
    case 403:
      errMsg = '服务器拒绝访问'
      break
    case 404:
      errMsg = '请求资源未找到'
      break
    case 500:
    case 502:
    case 503:
    case 504:
      errMsg = responseData.message || responseData.msg || '服务器开小差了，请稍后再试'
      break
    default:
      errMsg = responseData.message || responseData.msg || `请求失败，状态码：${statusCode}`
  }
  uni.showToast({
    title: errMsg,
    icon: 'none',
    duration: 2000,
  })
  // console.error(`[HTTP Error ${statusCode}]:`, responseData)
  reject({
    isHttpError: true,
    statusCode,
    message: errMsg,
    data: responseData,
  })
}

/**
 * 处理网络错误或uni.request执行错误
 * @param {object} err uni.request fail回调的错误对象
 * @param {Function} reject Promise的reject函数
 */
function handleNetworkError(err, reject) {
  let errMsg = '网络请求失败，请检查您的网络连接'
  if (err.errMsg && err.errMsg.includes('timeout')) {
    errMsg = '请求超时，请稍后再试'
  }
  uni.showToast({
    title: errMsg,
    icon: 'none',
    duration: 2000,
  })
  // console.error('[Network Error or uni.request Fail]:', err)
  reject({
    isNetworkError: true,
    message: errMsg,
    originalError: err,
  })
}

// -------------------- 导出便捷方法 --------------------
// 为了方便调用，可以封装常用的GET, POST等方法

export default {
  /**
   * GET请求
   * @param {string} url 请求路径 (不含baseURL)
   * @param {object} [data] 请求参数 (会自动拼接到URL上)
   * @param {object} [options] 其他uni.request的配置，如header
   */
  get: (url, data, options = {}) => {
    return request({
      ...options,
      url,
      method: 'GET',
      data,
    })
  },

  /**
   * POST请求
   * @param {string} url 请求路径 (不含baseURL)
   * @param {object} [data] 请求体数据
   * @param {object} [options] 其他uni.request的配置，如header
   */
  post: (url, data, options = {}) => {
    return request({
      ...options,
      url,
      method: 'POST',
      data,
    })
  },

  /**
   * PUT请求
   * @param {string} url 请求路径 (不含baseURL)
   * @param {object} [data] 请求体数据
   * @param {object} [options] 其他uni.request的配置，如header
   */
  put: (url, data, options = {}) => {
    return request({
      ...options,
      url,
      method: 'PUT',
      data,
    })
  },

  /**
   * DELETE请求
   * @param {string} url 请求路径 (不含baseURL)
   * @param {object} [data] 请求参数 (会自动拼接到URL上)
   * @param {object} [options] 其他uni.request的配置，如header
   */
  delete: (url, data, options = {}) => {
    return request({
      ...options,
      url,
      method: 'DELETE',
      data,
    })
  },

  /**
   * 通用请求方法，如果你想更灵活地控制所有uni.request参数
   * @param {object} options uni.request的完整配置对象 (url会自动拼接baseURL)
   */
  request: (options) => {
    return request(options)
  },
}
