<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnFormItem from '@tuniao/tnui-vue3-uniapp/components/form/src/form-item.vue'
import TnForm from '@tuniao/tnui-vue3-uniapp/components/form/src/form.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import TnSubsectionItem from '@tuniao/tnui-vue3-uniapp/components/subsection/src/subsection-item.vue'
import TnSubsection from '@tuniao/tnui-vue3-uniapp/components/subsection/src/subsection.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { formRules } from '@/pages/specialistPages/commit.js'
import { auditPerson, auditPersonTitle } from '@/pages/specialistPages/content.js'
import { jumpPageTo } from '@/utils/pageTo'
import { debounce, fuzzySearch } from '@/utils/tools.js'

defineOptions({
  options: { styleIsolation: 'shared' },
})

const searchValue = ref('')
const auditArr = ref([])
const showPopup = ref(false)
const subsectionValue = ref(0)
const formRef = ref()
const formData = ref({
  reason: '',
})

// 根据是否为小程序判断offsetTop
// function getIconOffsetTop() {
//   let top = -5
//   // #ifdef MP-WEIXIN
//   top = 0
//   // #endif
//   return top
// }

// 根据选项判断是否必填
watch(subsectionValue, (newVal) => {
  formRules.reason[0].required = newVal !== 0
})

auditArr.value = auditPerson

// 防抖模糊搜索
const debouncedSearch = debounce((val) => {
  auditArr.value = fuzzySearch(auditPerson, val)
}, 500)
function searchStudent(val) {
  debouncedSearch(val)
}

// 颜色判断
function approveBtn(item) {
  return item.uploadStatus ? '#00C595' : 'rgba(0, 197, 149, 0.32)'
}

function openPopup() {
  showPopup.value = true
}

// 审核通过情况
const APPROVE_PASS = [1, 2]

function cancelClick() {
  showPopup.value = false
  formData.value.reason = ''
}

function submitClick() {
  showPopup.value = false
  formData.value.reason = ''
}

// 扫码
function scanCustomCode() {
  uni.scanCode({
    scanType: 'qrCode',
    success() {
      // console.log(res)
      // qrcodeRes.value = res.result
    },
  })
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8]">
    <SafeTopArea>
      <view class="font-[PingFangSC]">
        <SafeArea tabbar="true">
          <view class="flex h-full flex-col gap-[20rpx] p-[20rpx]">
            <view class="relative h-[80rpx]">
              <Header title="审核" type="black" :hasBack="false" />
            </view>
            <view class=" rounded-[15rpx] bg-white">
              <TnInput v-model="searchValue" :border="false" placeholder="请输入姓名/身份证号" @change="searchStudent">
                <template #prefix>
                  <TnIcon name="search" />
                </template>
              </TnInput>
            </view>
            <scroll-view scroll-y class="flex-auto rounded-[20rpx]">
              <List :list="auditArr">
                <template v-if="auditArr.length > 0" #default="{ dataList }">
                  <view class="flex flex-col gap-[20rpx]">
                    <view v-for="(item, index) in dataList" :key="index" class="rounded-[40rpx] bg-[#FFFFFF]">
                      <!-- 信息头部 -->
                      <view class=" p-[40rpx]">
                        <view class="flex justify-between pb-[20rpx]">
                          <view class="flex gap-[20rpx]">
                            <text class="text-[30rpx] font-[500] leading-[40rpx] text-[#383838]">
                              {{ item.name }}
                            </text>
                            <text class="text-[28rpx] font-[400] leading-[40rpx] text-[#383838]">
                              {{ item.sex ? '男' : '女' }}
                            </text>
                            <text class="text-[28rpx] font-[400] leading-[40rpx] text-[#383838]">
                              {{ item.type ? '境外考生' : '境内考生' }}
                            </text>
                          </view>
                          <view class="flex gap-[20rpx]">
                            <view
                              :class="[item.uploadStatus ? 'bg-[#EBFFEE] text-[#2CBB6D]' : 'bg-[#EBF1FF] text-[#5377DA]']"
                              class="rounded-[10rpx] px-[20rpx] text-[20rpx] leading-[28rpx] flex-center"
                            >
                              {{ item.uploadStatus ? '已上传' : '未上传' }}
                            </view>
                            <view
                              :class="[item.approveStatus ? 'bg-[#EBFFEE] text-[#2CBB6D]' : 'bg-[#EBF1FF] text-[#5377DA]']"
                              class="rounded-[10rpx] px-[20rpx]  text-[20rpx] leading-[28rpx] flex-center"
                            >
                              {{ item.approveStatus ? '已审定' : ' 未审定' }}
                            </view>
                          </view>
                        </view>
                        <!-- 信息内容 -->
                        <view class="flex flex-col gap-[20rpx]">
                          <view
                            v-for="(i, key) in auditPersonTitle" :key="key"
                            class="flex flex-row gap-[10rpx] text-[25rpx] font-[400] leading-[46rpx]"
                          >
                            <!-- 标题部分 -->
                            <view class="flex w-[110rpx] shrink-0 justify-between">
                              <text v-for="(title, titleKey) in i.title" :key="titleKey" class="text-[#63676F] ">
                                {{ title }}
                              </text>
                            </view>
                            <text>
                              :
                            </text>
                            <!-- 内容部分 -->
                            <text class="text-[#424346]">{{ item[i.content] }}</text>
                          </view>
                        </view>
                        <!-- 操作 -->
                        <view
                          v-if="!APPROVE_PASS.includes(item.approveStatus)"
                          class="flex flex-row-reverse gap-[30rpx] pt-[20rpx]"
                        >
                          <TnButton
                            type="success" width="180rpx" height="70rpx" :text-color="approveBtn(item)"
                            :border-color="approveBtn(item)" plain :disabled="item.uploadStatus ? false : true"
                            :custom-style="{ borderRadius: '60rpx' }" @click="openPopup"
                          >
                            <text class="text-[24rpx] font-[500] leading-[33rpx]">
                              审核
                            </text>
                          </TnButton>
                          <TnButton
                            width="180rpx" height="70rpx" plain text-color="#454545" border-color="#DEDEDE"
                            :custom-style="{ borderRadius: '60rpx' }"
                            @click="jumpPageTo({ url: `/specialistPages/pages/upload/index?id=${item.id}` })"
                          >
                            <text class="text-[24rpx] font-[500] leading-[33rpx]">
                              上传材料
                            </text>
                          </TnButton>
                        </view>
                      </view>

                      <!-- 审定完成 -->
                      <view
                        v-if="item.approveStatus"
                        class="flex items-center justify-between rounded-b-[40rpx] bg-gradient-to-l from-[#FFFFFF] to-[#E8F1F8] p-[40rpx] py-[20rpx]"
                      >
                        <text class="text-[25rpx] font-[500] leading-[46rpx] text-[#424346]">
                          审核认定时间：{{ item.applyTime }}
                        </text>
                        <TnButton
                          width="180rpx" height="70rpx" plain text-color="#454545" border-color="#DEDEDE"
                          :custom-style="{ borderRadius: '60rpx' }"
                          @click="jumpPageTo({ url: `/specialistPages/pages/upload/index?id=${item.id}` })"
                        >
                          <text class="text-[24rpx] font-[500] leading-[33rpx]">
                            上传材料
                          </text>
                        </TnButton>
                      </view>
                    </view>
                  </view>
                </template>
              </List>
            </scroll-view>
            <TnPopup v-model="showPopup" width="80%">
              <view class="flex flex-col gap-[20rpx] rounded-[10px] border-black p-[20px]">
                <view>
                  <text class="text-[48rpx]">
                    审核
                  </text>
                </view>
                <view class="border-t pt-[20rpx]">
                  <text>
                    该考生的资格考核结果为
                  </text>
                </view>
                <view>
                  <TnSubsection v-model="subsectionValue" active-color="#00a096">
                    <TnSubsectionItem title="通过" />
                    <TnSubsectionItem title="不通过" />
                  </TnSubsection>
                </view>
                <view>
                  <TnForm ref="formRef" :model="formData" :rules="formRules" :hide-required-asterisk="true">
                    <TnFormItem label="说明" prop="reason">
                      <TnInput v-model="formData.reason" placeholder="审核不通过时为必填项" />
                    </TnFormItem>
                  </TnForm>
                </view>
                <view class="gap-[60rpx] flex-center">
                  <TnButton
                    type="success" width="120rpx" height="70rpx" bg-color="#d7d7d7" text-color="#fff"
                    @click="cancelClick"
                  >
                    取消
                  </TnButton>
                  <TnButton
                    type="success" width="120rpx" height="70rpx" bg-color="#00a096" text-color="#fff"
                    @click="submitClick"
                  >
                    确认
                  </TnButton>
                </view>
              </view>
            </TnPopup>
            <view class="fixed bottom-[180rpx] right-[40rpx]">
              <image src="/static/images/specialistIcon/scanCode.png" class="size-[102rpx]" @click="scanCustomCode" />
            </view>
          </view>
        </SafeArea>
      </view>
    </SafeTopArea>
  </view>
</template>

<style scoped>
:deep(.tn-subsection-item.is-active) {
  background-color: #00a096;
}
</style>
