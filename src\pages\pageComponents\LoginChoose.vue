<!-- 若用户是多角色，则登录后进入该页面进行身份选择 -->
<!-- 用户角色存入store中 -->
<script setup>
import Header from '@/components/Header'
import SafeArea from '@/components/SafeArea'
import SafeTopArea from '@/components/SafeTopArea'
import { roles } from '@/pages/tabbarPages/content'
import { useAuthStore } from '@/stores/modules/auth'
import { jumpPageTo } from '@/utils/pageTo'

const authStore = useAuthStore()
function chooseRole(item) {
  authStore.chooseRole(item)
  // 有效时间保持状态
  const loginData = {
    // 单角色时自动选择，多角色时手动选择
    loginStatus: authStore.role,
    time: new Date().getTime(),
    expire: 1024 * 1024 * 60 * 24 * 7,
  }
  // #ifndef MP-WEIXIN
  localStorage.setItem('loginData', JSON.stringify(loginData))
  // #endif

  // #ifdef MP-WEIXIN
  wx.setStorage({
    key: 'loginData',
    data: loginData,
  })
  // #endif
  jumpPageTo({ url: '/pages/tabbarPages/index' })
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8]">
    <SafeTopArea>
      <view class="font-[PingFangSC]">
        <SafeArea>
          <view class="flex h-full flex-col gap-[20rpx] p-[20rpx]">
            <view class="relative h-[80rpx]">
              <Header title="用户选择" type="black" :hasBack="false" />
            </view>
            <view
              class="chooseBg flex flex-col px-[70rpx] py-[35rpx] text-[30rpx] font-[500] leading-[42rpx] text-[#323232]"
            >
              <view
                v-for="(item, index) in authStore.userRoles" :key="index"
                style="background: url('/static/images/specialistIcon/selectBg.png') no-repeat ; background-size: 100% 100%"
                class="relative flex py-[46rpx] text-center"
                @click="chooseRole(item)"
              >
                <view class="m-auto">
                  {{ roles[item].name }}
                </view>
              </view>
            </view>
          </view>
        </SafeArea>
      </view>
    </SafeTopArea>
  </view>
</template>

<style scoped>
.chooseBg {
  background: url('@/static/images/specialistIcon/specHomeBg.png') no-repeat;
  background-size: 100% 100%;
}
</style>
