{"easycom": {"autoscan": true, "custom": {"^u--(.*)": "uview-plus/components/u-$1/u-$1.vue", "^up-(.*)": "uview-plus/components/u-$1/u-$1.vue", "^u-([^-].*)": "uview-plus/components/u-$1/u-$1.vue", "^tn-(.*)-(item|group)$": "@tuniao/tnui-vue3-uniapp/components/$1/src/$1-$2.vue", "^tn-(.*)": "@tuniao/tnui-vue3-uniapp/components/$1/src/$1.vue", "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "pages": [{"path": "pages/tabbarPages/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/pageComponents/WebView", "style": {"navigationStyle": "custom"}}, {"path": "pages/specialistPages/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/pageComponents/LoginChoose", "style": {"navigationStyle": "custom"}}, {"path": "pages/verifyPages/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/printPages/index", "style": {"navigationStyle": "custom"}}], "subpackages": [{"root": "homePages", "pages": [{"path": "pages/subscribe/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/successPage/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/affiche/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/eventDetail/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/afficheList/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/eventList/index", "style": {"navigationStyle": "custom"}}]}, {"root": "specialistPages", "pages": [{"path": "pages/upload/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/shift/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/selectStudent/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/selectSpecialist/index", "style": {"navigationStyle": "custom"}}]}, {"root": "auditPages", "pages": [{"path": "pages/verification/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/result/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/studentKnow/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/signaturePads/index", "style": {"pageOrientation": "landscape", "navigationStyle": "custom"}}, {"path": "pages/signSuccess/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/commit/index", "style": {"navigationStyle": "custom"}}]}], "globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "weapp-tailwindcss", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "app-plus": {"screenOrientation": ["portrait-primary", "portrait-secondary", "landscape-primary", "landscape-secondary"], "background": "#F8F8F8"}}