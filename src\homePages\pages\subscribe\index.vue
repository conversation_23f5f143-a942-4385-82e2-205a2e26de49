<script setup>
import { onLoad } from '@dcloudio/uni-app'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnNotify from '@tuniao/tnui-vue3-uniapp/components/notify/src/notify.vue'
import { getSubTime, postSubscribe } from '@/api/home/<USER>'
import BottomBtn from '@/components/BottomBtn.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { jumpPageTo } from '@/utils/pageTo'

const notifyRef = ref()
const activeIndex = ref() // 选择预约时间
const paging = ref(null)
const eventId = ref()// 活动Id
const subTimeList = ref([])
const startTime = ref('') // 开始时间

const systemInfo = uni.getSystemInfoSync()
const safeAreaInsets = systemInfo.safeAreaInsets || {} // 可能为 undefined
const topSafeHeight = safeAreaInsets.top || 0 // 顶部安全高度
const safeHeight = 1

// 失败提示
function failMeg() {
  notifyRef.value?.show({
    msg: '预约失败',
    position: 'center',
    type: 'warning',
  })
}

// 选择场次
function checkActive(item) {
  if (item.opened) {
    activeIndex.value = item.id
  }
  else {
    notifyRef.value?.show({
      msg: '该场次不可预约，请预约其他场次',
      position: 'center',
      type: 'warning',
    })
  }
}

// 确认预约
function subscribeClick() {
  if (activeIndex.value !== undefined) {
    const subscribeOption = {
      eventId: Number(eventId.value),
      timeSlotId: Number(activeIndex.value),
    }
    // console.log(subscribeOption)

    postSubscribe(subscribeOption).then(() => {
      jumpPageTo({ url: '/homePages/pages/successPage/index' })
    })
  }
  else {
    failMeg()
  }
}

function queryList(pageNo, pageSize) {
  const subOptions = {
    eventId: eventId.value,
    pageSize,
    pageNum: pageNo,
    orderByColumn: 1,
    isAsc: 'asc',
  }

  getSubTime(subOptions)
    .then((res) => {
      if (res.total === 0) {
        paging.value.complete(false)
        return
      }
      paging.value.completeByTotal(res.rows, res.total)
      startTime.value = res.rows[0].auditStartTime
      // console.log(res.rows)

      // console.log(startTime.value)
    })
    .catch(() => {
      paging.value.complete(false)
    })
}

onLoad((option) => {
  eventId.value = Number(option.id)
  console.log(eventId.value)
})
</script>

<template>
  <SafeTopArea>
    <List>
      <SafeArea class="flex flex-col bg-[#f8f8f8] font-[PingFangSC]">
        <view class="relative h-[180px] w-full bg-gradient-to-r from-[#66D0CA] to-[#79DAB7]">
          <Header title="活动预约" />
          <view
            class="absolute w-screen bg-gradient-to-r from-[#66D0CA] to-[#79DAB7]"
            :style="{ height: `${topSafeHeight + safeHeight}px`, top: `-${topSafeHeight - safeHeight}px` }"
          />
        </view>
        <!-- 盖住部分 Header -->
        <scroll-view scroll-y class="z-10 mt-[-100px] h-[calc(100%-70px)]">
          <view class="flex flex-col gap-[10px]">
            <view class="m-auto w-[95%] rounded-[5px] bg-white p-[14.5px]">
              <text class="text-[15px] font-[500] leading-[22.5px] text-[#202027]">
                国网江苏省电力有限公司2025年高校毕业生招聘考试资格审核活动
              </text>
              <view
                class="mt-[5px] flex flex-col  gap-[10px] border-t pt-[10px] text-[13px] font-[400] leading-[23px] text-[#63676F]"
              >
                <text>审核地点：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  国网江苏省电力有限公司技能培训中心（江苏省苏州市姑苏区劳动路599号）
                </text>
                <text>资格审核时间：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  2025-05-11 8:00-18:00
                </text>
                <text>活动说明：</text>
                <text class="text-[14px] leading-[21.5px] text-[#1D1D20]">
                  请提前10分钟到达技培中心门口，等待进入
                </text>
              </view>
            </view>

            <view class="m-auto w-[95%] rounded-[5px] bg-white p-[14.5px]">
              <view class=" flex flex-col gap-[10px] text-[14px] font-[400] leading-[27px] text-[#63676F]">
                <text>考生姓名：<text class="text-[#1D1D20]">韩梅梅</text></text>
                <text>身份证号：<text class="text-[#1D1D20]">320681200302267010</text></text>
                <text>手机号码：<text class="text-[#1D1D20]">18951326690</text></text>
                <text>考试专业：<text class="text-[#1D1D20]">电工类</text></text>
                <text>应聘单位：<text class="text-[#1D1D20]">国网江苏省电力有限公司</text></text>
              </view>
            </view>

            <view class="relative m-auto w-[95%] rounded-[5px] bg-white">
              <view class="selectSession absolute left-[-5px] top-[-5px] h-[34px] w-[110px]" />
              <text class="py-[10px] pt-[20px] text-[16px] font-[500] leading-[22.5px] text-[#333333] flex-center">
                {{ startTime.slice(0, 10) }}
              </text>
              <z-paging
                ref="paging" v-model="subTimeList" height="600rpx" :fixed="false"
                :auto-hide-loading-after-first-loaded="false" @query="queryList"
              >
                <view class="grid w-full grid-cols-2 gap-[10px] p-[10px]">
                  <view
                    v-for="(item, index) in subTimeList" :key="index"
                    :class="[item.opened ? 'bg-[#F2FFFA] text-[#34BAA3]' : 'bg-[#F6F8FA] text-[#7B8182]', item.id === activeIndex && item.opened ? 'border-[#34BAA3]' : '']"
                    class="relative flex flex-col gap-[10px] rounded-[10px] border py-[10px] text-[15px] font-[500] leading-[21px] flex-center"
                    @click="checkActive(item)"
                  >
                    <view
                      v-show="item.id === activeIndex && item.opened"
                      class="selected absolute bottom-0 right-0 size-[14px]"
                    />
                    <view class="flex gap-[5px]">
                      <TnIcon name="time" size="15px" />
                      <text>{{ item.auditStartTime.slice(11, 16) }}~{{ item.auditEndTime.slice(11, 16) }} </text>
                    </view>
                    <text>
                      {{ item.allocated === item.maxAllocation ? '满员' : `余${item.maxAllocation - (item.allocated ?? 0)}`
                      }}
                    </text>
                    <text>{{ item.opened ? '可预约' : '不可预约' }}</text>
                  </view>
                </view>
              </z-paging>
            </view>
          </view>
        </scroll-view>
        <TnNotify ref="notifyRef" />
        <!-- 底部按钮 -->
        <BottomBtn title="确认预约" @subClick="subscribeClick" />
      </SafeArea>
    </List>
  </SafeTopArea>
</template>

<style scoped>
.selectSession {
  background: url('@/static/images/homeIcon/selectSession.png') no-repeat;
  background-size: contain;
}

.selected {
  background: url('@/static/images/homeIcon/selected.png') no-repeat;
  background-size: contain;
}
</style>
