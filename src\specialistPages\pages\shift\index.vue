<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnListItem from '@tuniao/tnui-vue3-uniapp/components/list/src/list-item.vue'
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { shiftText } from '@/pages/specialistPages/content.js'
import { jumpPageTo } from '@/utils/pageTo'
</script>

<template>
  <SafeTopArea>
    <view class="font-[PingFangSC]">
      <view class="header relative top-[20rpx] h-[130rpx] w-full bg-white">
        <Header title="转移考生" type="black" @rightClick="submitClick">
          <TnButton bg-color="#04A889" text-color="#FFFFFF">
            <text class="text-[26rpx] font-[400] leading-[36rpx]">完成</text>
          </TnButton>
        </Header>
      </view>

      <view>
        <SafeArea header="true">
          <scroll-view scroll-y class="m-auto h-full bg-[#F5F6F8] pt-[43rpx]">
            <view class="w-100vw relative top-[-10px] m-auto bg-white  p-[20px] pb-0">
              <TnListItem
                v-for="(item, index) in shiftText"
                :key="index"
                right-icon="right" :custom-style="{ backgroundColor: 'transparent', borderBottom: '1px solid #EFEFEF' }"
                @click="jumpPageTo({ url: `/specialistPages/pages/${item.name}/index` })"
              >
                <text class="text-[28rpx] font-[400] leading-[40rpx] text-[#A2A2A2]">
                  {{ item.title }}
                </text>
              </TnListItem>
            </view>
          </scroll-view>
        </SafeArea>
      </view>
    </view>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
