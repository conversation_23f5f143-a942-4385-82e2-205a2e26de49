<script setup>
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnRadioGroup from '@tuniao/tnui-vue3-uniapp/components/radio/src/radio-group.vue'
import TnRadio from '@tuniao/tnui-vue3-uniapp/components/radio/src/radio.vue'
import BottomBtn from '@/components/BottomBtn.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { specialistArr } from '@/pages/specialistPages/content.js'
import { useSpecialistStore } from '@/stores/modules/specialist'
import { jumpPageBack } from '@/utils/pageTo'
import { debounce, fuzzySearch } from '@/utils/tools.js'

const selectSpecValue = ref()
const bottomTitle = ref('确定')
const studentList = ref([])
const searchValue = ref('')

const specialistStore = useSpecialistStore()

function submitClick() {
  // 提交选中的学生
  jumpPageBack()
}

// 防抖模糊搜索
const debouncedSearch = debounce((val) => {
  studentList.value = fuzzySearch(specialistArr, val)
}, 500)

function searchPerson(val) {
  debouncedSearch(val)
  // console.log('111')
}

onReady(() => {
  studentList.value = specialistArr
  selectSpecValue.value = specialistStore.specialist || ''
})

watch(() => selectSpecValue.value, (newVal) => {
  // 更新store中的专家选择值
  specialistStore.setSpecialist(specialistArr.find(item => item.id === newVal) || {})
})
</script>

<template>
  <SafeTopArea>
    <view class="font-[PingFangSC]">
      <view class="header relative h-[130rpx] w-full bg-white">
        <Header title="选择专家" type="black" @rightClick="submitClick" />
      </view>
      <SafeArea header="true" bottomBtn="true">
        <view class="relative m-auto flex h-full flex-col bg-white px-[20px]">
          <!-- 搜索 -->
          <view class="rounded-[15rpx] bg-[#F5F6F8]">
            <TnInput v-model="searchValue" :border="false" placeholder="搜索" @change="searchPerson">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 选择列表 -->
          <scroll-view scroll-y class="flex-auto overflow-hidden">
            <List :list="studentList">
              <template v-if="studentList.length > 0" #default="{ dataList }">
                <TnRadioGroup v-model="selectSpecValue" :wrap="true">
                  <TnRadio
                    v-for="(item, index) in dataList" :key="index" :label="item.id" checked-shape="circle"
                    :custom-style="{ borderBottom: '1px solid #E6E6E6' }"
                  >
                    <view class="flex gap-[20rpx] flex-center">
                      <view
                        class="size-[77rpx] rounded-[50%] bg-gradient-to-b from-[#76c1e4] to-[#b4e6ee] text-[50rpx] font-[600] text-white flex-center"
                      >
                        {{ item.name[0] }}
                      </view>
                      <text class="text-[30rpx] font-[400] leading-[106rpx]">
                        {{ item.name }}
                      </text>
                    </view>
                  </TnRadio>
                </TnRadioGroup>
              </template>
            </List>
          </scroll-view>
        </view>
        <BottomBtn :title="bottomTitle" @subClick="submitClick" />
      </SafeArea>
    </view>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
