import request from '@/utils/request'
/**
 * 登录
 * "clientId": '428a8310cd442757ae699df5d894f051',
 * "grantType": "sms",
 * "tenantId": "000000", //pc传啥 你这个就传啥 应该是这个
 * "idNumber": "", //身份证
 * "phonenumber": "", //手机号
 * "smsCode": "" //短信验证码
 */
export function userLogin(data) {
  return request.post('/auth/login',data)
}

// 选择角色接口
export function selectRole(role) {
  return request.post(`/auth/choose/${role}`)
}

// 获取短信验证码
export function getSmsCode(params){
  return request.get('/resource/sms/code',params)
}
