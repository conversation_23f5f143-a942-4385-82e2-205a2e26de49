<script setup>
import { getAfficheDetail } from '@/api/home/<USER>'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'

const affiche = ref({})

onLoad((option) => {
  const id = Number(option.id)
  getAfficheDetail({ id }).then((res) => {
    affiche.value = res
    const fileUrl = affiche.value.fileUrl
    affiche.value.fileUrl = fileUrl?.startsWith('/') ? 'http://*************:8081'.concat(fileUrl) : fileUrl
    console.log(affiche.value)
  })
})
</script>

<template>
  <SafeTopArea class="font-[PingFangSC]">
    <List>
      <view v-if="affiche.content">
        <view class="header relative top-[50rpx] h-[80rpx] w-full bg-white">
          <Header title="通知公告" type="black" />
        </view>
        <SafeArea header="true">
          <scroll-view scroll-y class="size-full rounded-[20rpx] bg-white pt-[100rpx]">
            <view class="flex flex-col gap-[25rpx] px-[44rpx]">
              <text class="text-[36rpx] font-[600] leading-[50rpx] text-[#000000]">
                {{ affiche.title }}
              </text>
              <text class="text-[26rpx] font-[400] leading-[37rpx] text-[#79797E]">
                {{ affiche.publishTime }}
              </text>
              <text class="fomt-[400] select-text text-[34rpx] leading-[60rpx] text-[#000000]">
                {{ affiche.content }}
              </text>
            </view>
          </scroll-view>
        </SafeArea>
      </view>
      <view v-if="!affiche.content">
        <web-view :src="affiche.fileUrl" />
      </view>
    </List>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
