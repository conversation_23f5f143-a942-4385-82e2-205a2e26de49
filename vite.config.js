import path from 'node:path'
import uni from '@dcloudio/vite-plugin-uni'
import AutoImport from 'unplugin-auto-import/vite'
import { defineConfig } from 'vite'
import { UnifiedViteWeappTailwindcssPlugin as uvtw } from 'weapp-tailwindcss/vite'
import { WeappTailwindcssDisabled } from './platform'
import postcssPlugins from './postcss.config'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  return {
    // uvtw 一定要放在 uni 后面
    plugins: [
      uni(),
      uvtw({
        rem2rpx: true,
        disabled: WeappTailwindcssDisabled,
      }),
      AutoImport({
        imports: ['vue', 'uni-app', 'pinia'],
        eslintrc: {
          enabled: true,
        },
      }),
    ],
    transpileDependencies: ['z-paging'],
    resolve: {
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    // 内联 postcss 注册 tailwindcss
    css: {
      postcss: {
        plugins: postcssPlugins,
      },
      // https://vitejs.dev/config/shared-options.html#css-preprocessoroptions
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api'],
        },
      },
    },
  }
})
