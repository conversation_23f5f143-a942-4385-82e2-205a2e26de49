<script setup>
import { computed, onMounted, ref, watch } from 'vue'

const props = defineProps({
  // 图片数组
  images: {
    type: Array,
    default: () => [],
  },
  // 是否显示加载状态
  loading: {
    type: Boolean,
    default: false,
  },
  // 图片间距
  spacing: {
    type: String,
    default: '20rpx',
  },
  // 图片圆角
  borderRadius: {
    type: String,
    default: '8rpx',
  },
  // 是否显示页码
  showPageNumber: {
    type: Boolean,
    default: true,
  },
})

const emit = defineEmits(['imageLoad', 'imageError', 'imageClick'])

// 图片加载状态
const imageLoadStates = ref({})
const allImagesLoaded = ref(false)

// 计算已加载的图片数量
const loadedCount = computed(() => {
  return Object.values(imageLoadStates.value).filter(Boolean).length
})

// 检查是否所有图片都已加载
function checkAllLoaded() {
  if (props.images.length > 0 && loadedCount.value === props.images.length) {
    allImagesLoaded.value = true
    emit('imageLoad', { allLoaded: true, loadedCount: loadedCount.value })
  }
}

// 图片加载成功
function onImageLoad(index) {
  imageLoadStates.value[index] = true
  checkAllLoaded()
  emit('imageLoad', { index, allLoaded: allImagesLoaded.value, loadedCount: loadedCount.value })
}

// 图片加载失败
function onImageError(index, error) {
  imageLoadStates.value[index] = false
  emit('imageError', { index, error })
}

// 预览图片
function previewImage(index) {
  uni.previewImage({
    current: index,
    urls: props.images,
  })
}

// 初始化加载状态
onMounted(() => {
  imageLoadStates.value = {}
  allImagesLoaded.value = false
})

// 监听图片数组变化
watch(() => props.images, () => {
  imageLoadStates.value = {}
  allImagesLoaded.value = false
}, { immediate: true })
</script>

<template>
  <view class="image-preview-container">
    <!-- 加载状态 -->
    <view v-if="loading || !allImagesLoaded" class="loading-container">
      <view class="loading-spinner" />
      <text class="loading-text">
        {{ loading ? '正在加载文档...' : `加载中 ${loadedCount}/${images.length}` }}
      </text>
    </view>

    <!-- 图片预览区域 -->
    <view v-if="images.length > 0" class="images-container">
      <view
        v-for="(imageUrl, index) in images"
        :key="index"
        class="image-item"
        :style="{ marginBottom: index < images.length - 1 ? spacing : '0' }"
      >
        <!-- 页码显示 -->
        <view v-if="showPageNumber" class="page-number">
          第 {{ index + 1 }} 页
        </view>

        <!-- 图片 -->
        <image
          :src="imageUrl"
          mode="widthFix"
          class="preview-image"
          :style="{ borderRadius }"
          @load="onImageLoad(index)"
          @error="onImageError(index, $event)"
          @click="previewImage(index)"
        />

        <!-- 图片加载失败提示 -->
        <view
          v-if="imageLoadStates[index] === false"
          class="error-placeholder"
          :style="{ borderRadius }"
        >
          <view class="error-icon">
            📄
          </view>
          <text class="error-text">图片加载失败</text>
          <text class="retry-text" @click="onImageLoad(index)">点击重试</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-container">
      <view class="empty-icon">
        📄
      </view>
      <text class="empty-text">暂无文档内容</text>
    </view>
  </view>
</template>

<style scoped lang="scss">
.image-preview-container {
  width: 100%;
  min-height: 400rpx;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;

  .loading-spinner {
    width: 60rpx;
    height: 60rpx;
    border: 4rpx solid #f3f3f3;
    border-top: 4rpx solid #3e89fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20rpx;
  }

  .loading-text {
    color: #666;
    font-size: 28rpx;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.images-container {
  width: 100%;
}

.image-item {
  position: relative;
  width: 100%;

  .page-number {
    position: absolute;
    top: -40rpx;
    left: 0;
    color: #666;
    font-size: 24rpx;
    z-index: 10;
    background: rgba(255, 255, 255, 0.9);
    padding: 4rpx 12rpx;
    border-radius: 12rpx;
  }

  .preview-image {
    width: 100%;
    display: block;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;

    &:active {
      transform: scale(0.98);
    }
  }

  .error-placeholder {
    width: 100%;
    min-height: 400rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
    border: 2rpx dashed #ddd;

    .error-icon {
      font-size: 80rpx;
      margin-bottom: 20rpx;
    }

    .error-text {
      color: #999;
      font-size: 28rpx;
      margin-bottom: 20rpx;
    }

    .retry-text {
      color: #3e89fa;
      font-size: 26rpx;
      padding: 10rpx 20rpx;
      border: 2rpx solid #3e89fa;
      border-radius: 20rpx;
    }
  }
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;

  .empty-icon {
    font-size: 120rpx;
    margin-bottom: 30rpx;
    opacity: 0.5;
  }

  .empty-text {
    color: #999;
    font-size: 32rpx;
  }
}
</style>
