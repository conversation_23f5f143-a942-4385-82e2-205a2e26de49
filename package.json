{"name": "uni-vite-uview-template", "version": "0.0.0", "scripts": {"dev": "uni -p mp-weixin", "build": "npm run build:mp-weixin", "dev:app": "uni -p app", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "open:dev": "weapp open -p dist/dev/mp-weixin", "open:build": "weapp open -p dist/build/mp-weixin", "weapp:login": "weapp login", "upload:dev": "weapp upload -p dist/dev/mp-weixin -v 1.0.0 -d \"dev version\"", "upload:build": "weapp upload -p dist/build/mp-weixin -v 1.0.0 -d \"release version\"", "postinstall": "weapp-tw patch", "lint": "eslint .", "lint:fix": "eslint ./src --fix", "commit": "cz", "prepare": "husky install", "up:pkg": "pnpm up -rLi \"!@dcloudio/*\"", "up:uniapp": "pnpx @dcloudio/uvm@latest"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4060520250512001", "@dcloudio/uni-app-harmony": "3.0.0-4060520250512001", "@dcloudio/uni-app-plus": "3.0.0-4060520250512001", "@dcloudio/uni-components": "3.0.0-4060520250512001", "@dcloudio/uni-h5": "3.0.0-4060520250512001", "@dcloudio/uni-mp-alipay": "3.0.0-4060520250512001", "@dcloudio/uni-mp-baidu": "3.0.0-4060520250512001", "@dcloudio/uni-mp-harmony": "3.0.0-4060520250512001", "@dcloudio/uni-mp-jd": "3.0.0-4060520250512001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4060520250512001", "@dcloudio/uni-mp-lark": "3.0.0-4060520250512001", "@dcloudio/uni-mp-qq": "3.0.0-4060520250512001", "@dcloudio/uni-mp-toutiao": "3.0.0-4060520250512001", "@dcloudio/uni-mp-weixin": "3.0.0-4060520250512001", "@dcloudio/uni-mp-xhs": "3.0.0-4060520250512001", "@dcloudio/uni-quickapp-webview": "3.0.0-4060520250512001", "@dcloudio/uni-ui": "^1.5.7", "@tuniao/tn-icon": "^1.11.0", "@tuniao/tn-style": "^1.0.20", "@tuniao/tnui-vue3-uniapp": "^1.0.23", "@uqrcode/js": "^4.0.7", "@vue/shared": "3.4.21", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "libphonenumber-js": "^1.12.9", "mockjs": "^1.1.0", "pdfjs-dist": "3.9.179", "pinia": "2.2.4", "qrcode": "^1.5.4", "tnuiv3p-tn-dropdown": "^1.0.3", "tnuiv3p-tn-sign-board": "^1.0.2", "uview-plus": "^3.4.11", "vconsole": "^3.15.1", "vue": "3.4.21", "vue-demi": "0.14.6", "vue-i18n": "^9.14.2", "vue3-pdf-app": "^1.0.3", "weapp-qrcode-canvas-2d": "^1.1.6", "z-paging": "^2.8.6"}, "devDependencies": {"@commitlint/cli": "^19.7.1", "@commitlint/config-conventional": "^19.7.1", "@dcloudio/types": "^3.4.12", "@dcloudio/uni-automator": "3.0.0-4060520250512001", "@dcloudio/uni-cli-shared": "3.0.0-4060520250512001", "@dcloudio/uni-stacktracey": "3.0.0-4060520250512001", "@dcloudio/vite-plugin-uni": "3.0.0-4060520250512001", "@egoist/tailwindcss-icons": "^1.9.0", "@icebreakers/eslint-config": "^1.1.3", "@icebreakers/stylelint-config": "^1.0.0", "@iconify-json/mdi": "^1.2.3", "@iconify-json/svg-spinners": "^1.2.2", "@types/node": "^22.15.19", "@vue/runtime-core": "3.4.21", "autoprefixer": "^10.4.21", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.4.0", "eslint": "^9.27.0", "husky": "^9.1.7", "postcss": "^8.5.3", "sass": "^1.89.0", "sass-loader": "10.4.1", "tailwindcss": "^3.4.17", "unplugin-auto-import": "^19.2.0", "vite": "5.2.8", "weapp-ide-cli": "^2.0.12", "weapp-tailwindcss": "^4.1.7"}, "pnpm": {"onlyBuiltDependencies": ["vue-demi", "weapp-tailwindcss"]}}