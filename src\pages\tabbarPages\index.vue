<script setup>
import UniTransition from '@dcloudio/uni-ui/lib/uni-transition/uni-transition.vue'
import Tabbar from '@/components/Tabbar.vue'
import Login from '@/pages/pageComponents/Login.vue'
import Print from '@/pages/printPages/index.vue'
import Specialist from '@/pages/specialistPages/index.vue'
import Audit from '@/pages/tabbarPages/components/Audit.vue'
import Flow from '@/pages/tabbarPages/components/Flow.vue'
import Home from '@/pages/tabbarPages/components/Home.vue'
import Mine from '@/pages/tabbarPages/components/Mine.vue'
import Verify from '@/pages/verifyPages/index.vue'
import { useAuthStore } from '@/stores/modules/auth'
import { useTabbarStore } from '@/stores/modules/tabbar'

const authStore = useAuthStore()
const tabbarStore = useTabbarStore()

const loginPerson = ref()

const PERSON = {
  STUD: 0,
  VERIFY: 1,
  PRINT: 2,
  SPEC: 3,
}

const loginAniRef = ref()
const showPage = ref(false)

const pageList = [
  {
    key: 'home',
  },
  {
    key: 'audit',
  },
  {
    key: 'flow',
  },
  {
    key: 'mine',
  },
]
// 当前所在页面
const currentPage = ref(pageList[0])

function changePage(item) {
  // console.log(item, 'item')
  currentPage.value = pageList.find(page => page.key === item.key)
}

function loginSuccess(index) {
  loginPerson.value = index
  // loginAniRef.value.step({
  //  opacity: 0,
  // })
}

watch(() => authStore.showLogin, (newV) => {
  if (newV) {
    setTimeout(() => {
      showPage.value = false
    }, 1000)
  }
  if (!newV) {
    showPage.value = true
    loginAniRef.value.step({
      opacity: 0,
    })
    loginAniRef.value.run()
  }
}, {
  deep: true,
})

watch(() => tabbarStore.currentActiveTabbarIndex, (newV) => {
  currentPage.value = pageList[newV]
  // console.log(currentPage.value)

  // #ifndef MP-WEIXIN
  sessionStorage.setItem('tabbar', JSON.stringify(tabbarStore.currentActiveTabbarIndex))
  // #endif
  // #ifdef MP-WEIXIN
  wx.setStorage({
    key: 'tabbar',
    data: tabbarStore.currentActiveTabbarIndex,
  })
  // #endif
})

onReady(() => {
  // console.log(loginPerson.value)

  loginAniRef.value.init({
    duration: 1000,
    delay: 0,
  })
  // 判断登录状态达到自动登录目的
  // #ifndef MP-WEIXIN
  const loginData = JSON.parse(localStorage.getItem('loginData') || 0)
  if (loginData) {
    // 是否在有效期
    const now = new Date().getTime()
    if (now - loginData.time > 0) {
      authStore.login()
      loginPerson.value = loginData.loginStatus
    }
    else {
      localStorage.removeItem('loginData')
    }
  }
  // #endif

  // #ifdef MP-WEIXIN
  wx.getStorage({
    key: 'loginData',
    success(res) {
      const now = new Date().getTime()
      if (now - res.data.time > 0) {
        authStore.login()
        loginPerson.value = res.data.loginStatus
        // console.log(loginPerson.value);
      }
      else {
        wx.removeStorage({
          key: 'loginData',
        })
      }
    },
  })
  // #endif

  // 回到之前浏览页面
  // #ifndef MP-WEIXIN
  const tabbar = sessionStorage.getItem('tabbar') || 0
  tabbarStore.currentActiveTabbarIndex = JSON.parse(tabbar)
  // #endif

  // #ifdef MP-WEIXIN
  wx.getStorage({
    key: 'tabbar',
    success(res) {
      tabbarStore.currentActiveTabbarIndex = res.data
    },
    fail() {
      tabbarStore.currentActiveTabbarIndex = 0
    },
  })
  // #endif
})
</script>

<template>
  <view>
    <UniTransition ref="loginAniRef" :show="authStore.showLogin" class="absolute left-0 top-0 z-[99]">
      <view>
        <Login @login-success="loginSuccess" />
      </view>
    </UniTransition>
    <view>
      <!-- 学生页面 -->
      <view v-if="loginPerson === PERSON.STUD && authStore.showLogin === false">
        <Home v-if="currentPage.key === 'home'" />
        <Audit v-if="currentPage.key === 'audit'" />
        <Mine v-if="currentPage.key === 'mine'" />
        <Flow v-if="currentPage.key === 'flow'" />
        <Tabbar @change-switch-pages="changePage" />
      </view>
      <!-- 核验员页面 -->
      <view v-if="loginPerson === PERSON.VERIFY && authStore.showLogin === false">
        <Verify />
      </view>
      <!-- 打印员页面 -->
      <view v-if="loginPerson === PERSON.PRINT && authStore.showLogin === false">
        <Print />
      </view>
      <!-- 专家页面 -->
      <view v-if="loginPerson === PERSON.SPEC && authStore.showLogin === false">
        <Specialist />
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
