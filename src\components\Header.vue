<script setup>
import { jumpPageBack } from '@/utils/pageTo'

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  hasBack: {
    type: Boolean,
    default: true,
  },
  type: {
    type: String,
    default: 'white',
  },
  right: {
    type: String,
  },
})
const emits = defineEmits(['rightClick'])
const rightIconUrl = props.right !== undefined ? `/static/images/auditIcon/${props.right}.png` : ''
function handleClick() {
  emits('rightClick')
}
</script>

<template>
  <!-- 顶部标题和返回按钮 -->
  <view class="absolute left-0 flex w-full items-center justify-between px-3">
    <!-- 返回按钮 -->
    <view v-if="hasBack" class="flex h-full w-10 items-center" @click="jumpPageBack">
      <image v-if="type === 'white'" src="/static/images/back.png" class="size-[44rpx]" />
      <image v-if="type === 'black'" src="/static/images/back1.png" class="size-[44rpx]" />
    </view>
    <view v-else class="h-full w-10" />

    <!-- 标题 -->
    <view class="flex-1 text-center">
      <text class="text-[34rpx] font-bold" :class="[props.type === 'white' ? 'text-white' : 'text-black']">
        {{ title }}
      </text>
    </view>

    <!-- 占位 -->
    <view class="flex h-full w-10 items-center" @click="handleClick">
      <slot />
      <image v-if="right !== undefined" :src="rightIconUrl" class="size-[50rpx]" />
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
