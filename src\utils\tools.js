// 模糊搜索
export function fuzzySearch(arr, keyword) {
  if (!keyword) {
    return arr
  }

  const lowerKeyword = keyword.toLowerCase()

  return arr.filter((item) => {
    // 获取对象的所有值
    const values = Object.values(item)

    // 检查是否有任一值包含关键词
    return values.some((value) => {
      // 跳过undefined和null
      if (value == null) {
        return false
      }

      // 如果是字符串，直接比较
      if (typeof value === 'string') {
        return value.toLowerCase().includes(lowerKeyword)
      }
      // 如果是数字，转换为字符串比较
      else if (typeof value === 'number') {
        return value.toString().includes(keyword)
      }
      // 其他类型可以添加更多处理
      return false
    })
  })
}

// 防抖
export function debounce(func, delay) {
  let timeout
  return function (...args) {
    const _this = this

    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => {
      func.apply(_this, args)
    }, delay)
  }
}
