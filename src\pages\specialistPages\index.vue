<script setup>
import Tabbar from '@/components/Tabbar.vue'
import Audit from '@/pages/specialistPages/components/Audit.vue'
import Home from '@/pages/specialistPages/components/Home.vue'
import { useTabbarStore } from '@/stores/modules/tabbar'

const tabbarStore = useTabbarStore()

const pageList = [
  {
    key: 'home',
  },
  {
    key: 'audit',
  },
]
const currentPage = ref(pageList[0])

function changePage(item) {
  // console.log(item, 'item')
  currentPage.value = pageList.find(page => page.key === item.key)
}

watch(() => tabbarStore.currentActiveTabbarIndex, (newV) => {
  currentPage.value = pageList[newV]
  // #ifndef MP-WEIXIN
  sessionStorage.setItem('tabbar', JSON.stringify(tabbarStore.currentActiveTabbarIndex))
  // #endif
  // #ifdef MP-WEIXIN
  wx.setStorage({
    key: 'tabbar',
    data: tabbarStore.currentActiveTabbarIndex,
  })
  // #endif
})
</script>

<template>
  <Home v-if="currentPage.key === 'home'" />
  <Audit v-if="currentPage.key === 'audit'" />
  <Tabbar tabbarType="specialist" @change-switch-pages="changePage" />
</template>

<style scoped></style>
