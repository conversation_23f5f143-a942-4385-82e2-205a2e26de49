<script setup>
import { onLoad } from '@dcloudio/uni-app'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnImageUpload from '@tuniao/tnui-vue3-uniapp/components/image-upload/src/image-upload.vue'
import TnTitle from '@tuniao/tnui-vue3-uniapp/components/title/src/title.vue'
import Header from '@/components/Header.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { auditPerson } from '@/pages/specialistPages/content.js'

const imageList = ref([])
const person = ref({})

const identityUrl = 'http://************:8090/upload/image'
const studentUrl = 'http://************:8090/upload/image'
const applicationUrl = 'http://************:8090/upload/image'

onLoad((option) => {
  person.value = auditPerson[option.id]
})

// 根据是否为小程序判断offsetTop
function getIconOffsetTop() {
  let top = -5
  // #ifdef MP-WEIXIN
  top = 0
  // #endif
  return top
}
</script>

<template>
  <view class="min-h-screen bg-[#eeeeee]">
    <SafeTopArea>
      <view class="relative h-[80px] w-full bg-white">
        <Header title="上传资料" type="black" />
      </view>
      <view class="pt-[20px]">
        <scroll-view
          scroll-y
          class="overflow-hidden rounded-[20px] bg-white p-[15px] ifdef-[MP-WEIXIN]:max-h-[calc(100vh-230px)] ifndef-[MP-WEIXIN]:max-h-[calc(100vh-180px)]"
        >
          <view>
            <TnTitle title="考生信息" mode="vLine" assist-color="#00a096" size="xl" />
            <view>
              <view class="flex gap-[20px]">
                <text class="font-[600]">
                  {{ person.name }}
                </text>
                <text>
                  {{ person.sex ? '男' : '女' }}
                </text>
                <text>
                  {{ person.type ? '境外考生' : '境内考生' }}
                </text>
              </view>
              <view class="grid grid-cols-2 gap-[5px] gap-x-[60px] py-[10px]">
                <view class="flex">
                  <TnIcon name="identity" size="20px" color="#37aaa3" :offset-top="getIconOffsetTop()" />
                  {{ person.identity }}
                </view>
                <view class="flex">
                  <TnIcon name="tel" size="20px" color="#37aaa3" :offset-top="getIconOffsetTop()" />
                  {{ person.phone }}
                </view>
                <text>
                  院校：{{ person.school }}
                </text>
                <text>
                  专业：{{ person.specialty }}
                </text>
                <text>
                  考试层次：{{ person.level ? person.level === 1 ? '硕士' : '博士' : '本科' }}
                </text>
                <text>
                  考试专业：{{ person.examType }}
                </text>
              </view>
            </view>
          </view>
          <view class="py-[20px] pb-[40px]">
            <TnTitle title="材料上传" mode="vLine" assist-color="#00a096" size="xl" />
            <view>
              <view class="mt-[10px] border border-[#e0e0e0] p-[10px]">
                <text>1.身份证</text>
                <view class="mt-[10px] border-t border-solid border-black  pt-[10px]">
                  <TnImageUpload v-model="imageList" :action="identityUrl" />
                </view>
              </view>
              <view class="mt-[10px] border border-[#e0e0e0] p-[10px]">
                <text>2.学生证</text>
                <view class="mt-[10px] border-t border-solid border-black  pt-[10px]">
                  <TnImageUpload v-model="imageList" :action="studentUrl" />
                </view>
              </view>
              <view class="mt-[10px] border border-[#e0e0e0] p-[10px]">
                <text>3.申请表</text>
                <view class="mt-[10px] border-t border-solid border-black  pt-[10px]">
                  <TnImageUpload v-model="imageList" :action="applicationUrl" />
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <view class="fixed bottom-0 h-[70px] w-full bg-[#008880] text-[28px] text-white flex-center">
        <text>提交</text>
      </view>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
