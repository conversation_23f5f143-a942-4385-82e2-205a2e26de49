import dayjs from 'dayjs'
import Mock from 'mockjs'

const time = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
export const tabs = ['考生登录', '工作人员登录']

export const loginText = `您好，
欢迎使用江苏电力招聘`

// 固定活动开展地点
export const eventLocation = '国网江苏技能培训中心'

export const loginPupText = '是否同意以下协议'

// 核验信息数据
export const auditCardsArr = [
  {
    name: '张三',
    sex: 1,
    status: 1,
    identity: '320681200302267010',
    session: '09:00-11:00',
    transit: '2025-05-01 10:00:00',
  },
  {
    name: '张三',
    sex: 1,
    status: 0,
    identity: '320681200302267010',
    session: '09:00-11:00',
  },
  {
    name: '张三',
    sex: 0,
    status: -1,
    identity: '320681200302267010',
    session: '09:00-11:00',
  },
  {
    name: '李四',
    sex: 1,
    status: -1,
    identity: '320681200302267010',
    session: '09:00-11:00',
  },
  {
    name: '张三',
    sex: 0,
    status: 1,
    identity: '320681200302267010',
    session: '09:00-11:00',
    transit: '2025-05-01 12:00:00',
  },
]

// 打印信息数据
export const printCardsArr = [
  {
    name: '张三',
    sex: 1,
    printStatus: 1,
    identity: '320681200302267010',
    phone: '18951326690',
    transit: '2025-05-01 10:00:00',
  },
  {
    name: '张三',
    sex: 1,
    printStatus: 0,
    identity: '320681200302267010',
    phone: '18951326690',
  },
  {
    name: '张三',
    sex: 0,
    printStatus: 0,
    identity: '320681200302267010',
    phone: '18951326690',
  },
  {
    name: '李四',
    sex: 1,
    printStatus: 0,
    identity: '320681200302267010',
    phone: '18951326690',
  },
  {
    name: '张三',
    sex: 0,
    printStatus: 1,
    identity: '320681200302267010',
    phone: '18951326690',
    transit: '2025-05-01 12:00:00',
  },
]

// 导航栏数据
export const tabbarData = [
  {
    name: '首页',
    icon: '/static/images/tabbarIcon/home.png',
    activeIcon: '/static/images/tabbarIcon/home-fill.png',
    key: 'home',
  },
  {
    name: '审核',
    icon: '/static/images/tabbarIcon/audit.png',
    activeIcon: '/static/images/tabbarIcon/audit-fill.png',
    key: 'audit',
  },
  {
    name: '流程',
    icon: '/static/images/tabbarIcon/flow.png',
    activeIcon: '/static/images/tabbarIcon/flow-fill.png',
    key: 'flow',
  },
  {
    name: '我的',
    icon: '/static/images/tabbarIcon/mine.png',
    activeIcon: '/static/images/tabbarIcon/mine-fill.png',
    key: 'mine',
  },

]

export const specialistTabbarData = [
  {
    name: '首页',
    icon: '/static/images/tabbarIcon/home.png',
    activeIcon: '/static/images/tabbarIcon/home-fill.png',
    key: 'home',
  },
  {
    name: '审核',
    icon: '/static/images/tabbarIcon/audit.png',
    activeIcon: '/static/images/tabbarIcon/audit-fill.png',
    key: 'audit',
  },
]

// 考生须知
export const studentKnow = {
  title: '2025年国网招聘考生须知',
  hint: '2025年5月31日起，招聘考试不再提供纸质《考生须知》。考生可在阅读并下载电子版考生须知，本《考生须知》主要说明考试注意事项、考试联系电话与考场分布图等信息，请考生务必详细阅读。',
  content: `亲爱的考生：
  欢迎参加江苏省电网招聘资格审核！为确保审核顺利进行，请仔细阅读以下须知：
  一、审核时间与地点
  时间：请务必在规定时间内到场，逾期视为自动放弃。
  地点：[详细地址]，请提前规划路线，预留充足时间。
  二、审核流程
  入场核验：抵达后至入口处，配合身份审核人员完成人证比对。若核验失败，请服从工作人员安排，进行二次研判。
  三、携带材料
  身份证原件（有效期内）。
  学历证书、学位证书原件及复印件（应届毕业生提供就业推荐表）。`,
}

// 签署
export const sign = {
  title: '2025年国网招聘境内考生承诺书',
  hint: '参加高校毕业生招聘及资格审核的所有考生，需阅读并签署《毕业生承诺书》，对本人相关信息、资质条件的真实性和有效性作出承诺，并对考试试题履行保密承诺（在考试结束后不讨论、不对外泄露考试题目）。',
  // 自动填充部分字段（接口尚未完善）
  content: {
    // 境内
    inCountry: ``,
    // 境外
    outCountry: ``,
  },
}

// 核验通过原因
export const reasonOptions = [
  { label: '考生容貌与证件照片存在差异，经核验，确为本人。', value: 1 },
  { label: '考生所持证件芯片损坏无法读取，经核验，确为本人。', value: 2 },
  { label: '考生使用临时身份证无法读取，经核验，确为本人。', value: 3 },
  { label: '其他原因', value: 4 },
]

// 预约信息
export const subscribeList = Mock.mock({
  'list|5-10': [
    {
      'id|+1': 0,
      'time': function () {
        let startHour = 8 + (this.id % 12)
        let endHour = startHour + 1
        startHour = startHour < 10 ? `0${startHour}` : startHour
        endHour = endHour < 10 ? `0${endHour}` : endHour
        return `${startHour}:00-${endHour}:00`
      },
      'num|0-70': 0,
      'status': function () {
        return this.num === 70 ? 0 : 1
      },
    },
  ],
}).list

// 通知公告
export const affiche = `亲爱的考生：
欢迎参加江苏省电网招聘资格审核！为确保审核顺利进行，请仔细阅读以下须知：
一、审核时间与地点
时间：请务必在规定时间内到场，逾期视为自动放弃。
地点：[详细地址]，请提前规划路线，预留充足时间。
二、审核流程
入场核验：抵达后至入口处，配合身份审核人员完成人证比对。若核验失败，请服从工作人员安排，进行二次研判。
三、携带材料
身份证原件（有效期内）。
学历证书、学位证书原件及复印件（应届毕业生提供就业推荐表）。
亲爱的考生：
欢迎参加江苏省电网招聘资格审核！为确保审核顺利进行，请仔细阅读以下须知：
一、审核时间与地点
时间：请务必在规定时间内到场，逾期视为自动放弃。
地点：[详细地址]，请提前规划路线，预留充足时间。
二、审核流程
入场核验：抵达后至入口处，配合身份审核人员完成人证比对。若核验失败，请服从工作人员安排，进行二次研判。
三、携带材料
身份证原件（有效期内）。
学历证书、学位证书原件及复印件（应届毕业生提供就业推荐表）。`

// 流程步骤信息
export const flowList = [
  {
    id: 0,
    name: '身份核验',
    key: 'ver',
    noIcon: 'verNo',
    nowIcon: 'verNow',
    okIcon: 'verOk',
  },
  {
    id: 1,
    name: '承诺书签订',
    key: 'sign',
    noIcon: 'signNo',
    nowIcon: 'signNow',
    okIcon: 'signOk',
  },
  {
    id: 2,
    name: '资格审核',
    key: 'audit',
    noIcon: 'auditNo',
    nowIcon: 'auditNow',
    okIcon: 'auditOk',
  },
  {
    id: 3,
    name: '打印准考证',
    key: 'print',
    noIcon: 'printNo',
    nowIcon: 'printNow',
    okIcon: 'printOk',
  },
  {
    id: 4,
    name: '离开技培',
    key: 'out',
    noIcon: 'outNo',
    nowIcon: 'outNow',
    okIcon: 'outOk',
  },
]

// 个人中心列表数据
export const mineList = [
  {
    id: 0,
    name: '服务协议',
  },
  {
    id: 1,
    name: '隐私政策',
  },
  {
    id: 2,
    name: '备案号',
  },
  {
    id: 3,
    name: '退出登录',
  },
]

// 应聘专业选项
export const majorOptions = [
  {
    value: 0,
    label: '电工类',
  },
  {
    value: 1,
    label: '计算机类',
  },
  {
    value: 2,
    label: '通信类',
  },
  {
    value: 3,
    label: '财汇类',
  },
]

// 学历层次选项
export const levelOptions = [
  {
    value: 0,
    label: '硕士',
  },
  {
    value: 1,
    label: '博士',
  },
]

// 用户角色
export const roles = [
  {
    id: 0,
    name: '学生',
  },
  {
    id: 1,
    name: '核验员',
  },
  {
    id: 2,
    name: '打印员',
  },
  {
    id: 3,
    name: '专家',
  },
]
