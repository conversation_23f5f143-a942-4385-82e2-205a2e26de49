<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'

const props = defineProps({
  title: {
    type: String,
    default: '页面标题',
  },
  icon: {
    type: String,
  },
})

const emit = defineEmits(['subClick'])

function handleClick() {
  emit('subClick', 1)
}
</script>

<template>
  <view class="fixed bottom-0 z-20 w-full bg-white p-[20rpx] flex-center">
    <slot />
    <TnButton
      bg-color="#00a096" text-color="#fff" :custom-style="{ flex: '1' }" height="90rpx" font-size="40rpx"
      @click="handleClick"
    >
      <text v-if="props.icon === undefined">
        {{ props.title }}
      </text>
      <text v-else>
        <TnIcon :name="props.icon" size="40rpx" />
        <text>{{ props.title }}</text>
      </text>
    </TnButton>
  </view>
</template>

<style></style>
