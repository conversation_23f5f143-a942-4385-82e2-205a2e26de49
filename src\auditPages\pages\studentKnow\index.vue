<script setup>
import { onLoad } from '@dcloudio/uni-app'
import Acknowledge from '@/components/Acknowledge.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { studentKnow } from '@/pages/tabbarPages/content'
import { jumpPageTo } from '@/utils/pageTo'

const isGet = ref(false)
const isBottom = ref(false)
const eventId = ref()
onLoad((option) => {
  eventId.value = Number(option.id)
})

// 下载文件
function downloadFile() {
  uni.downloadFile({
    url: '/static/pdf/studKnow.pdf',
    success(res) {
      const filePath = res.tempFilePath
      uni.openDocument({
        filePath,
        fileType: 'pdf',
      })
    },
  })
}

// 当内容滑动底部时
function scrolltolower() {
  isBottom.value = true
  // console.log(isBottom.value)
}

// 跳转到签名
function studKnowClick() {
  jumpPageTo({ url: `/auditPages/pages/signaturePads/index?id=${eventId.value}` })
}

// 当内容不需要滚动时
onMounted(() => {
  const query = uni.createSelectorQuery()
  query.select('.scroll-view-container').boundingClientRect((data) => {
    query.select('.scroll-view-content').boundingClientRect((res) => {
      if (res && data && res.height <= data.height && !isGet.value) {
        isGet.value = true
        isBottom.value = true
      }
    }).exec()
  }).exec()
})
</script>

<template>
  <SafeTopArea>
    <List>
      <view class="font-[PingFangSC]">
        <view class="header relative top-[50rpx] h-[80rpx] w-full bg-white">
          <Header title="考生须知" type="black" right="download" @rightClick="downloadFile" />
        </view>
        <SafeArea header="true">
          <scroll-view
            scroll-y class="scroll-view-container m-auto h-full bg-white p-[44rpx] pb-[120rpx]"
            @scrolltolower="scrolltolower"
          >
            <view class="scroll-view-content">
              <text class="text-[34rpx] font-[400] leading-[60rpx]">
                {{ studentKnow.content }}
              </text>
            </view>
          </scroll-view>
        </SafeArea>
        <Acknowledge :isBottom="isBottom" title="考生须知" @studKnow="studKnowClick" />
      </view>
    </List>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
