<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <link rel="preload" as="style" crossorigin href="https://fontsapi.zeoseven.com/36/main/result.css"
    onload="this.rel='stylesheet'" onerror="this.href='https://fontsapi-storage.zeoseven.com/36/main/result.css'" />
  <noscript>
    <link rel="stylesheet" href="https://fontsapi.zeoseven.com/36/main/result.css" />
  </noscript>
  <link rel="stylesheet" type="text/css" href="./src/assets/style/index.css">
  <script>
    var coverSupport =
      'CSS' in window &&
      typeof CSS.supports === 'function' &&
      (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'))
    document.write(
      '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
      (coverSupport ? ', viewport-fit=cover' : '') +
      '" />',
    )
  </script>
  <title></title>
  <!--preload-links-->
  <!--app-context-->
</head>

<body>
  <div id="app"><!--app-html--></div>
  <script type="module" src="/src/main.js"></script>
</body>

</html>
