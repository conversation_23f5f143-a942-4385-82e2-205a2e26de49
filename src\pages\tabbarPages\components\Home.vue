<script setup>
import { onReady } from '@dcloudio/uni-app'
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import { getAfficheList } from '@/api/home/<USER>'
import { getEventList } from '@/api/home/<USER>'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { eventLocation } from '@/pages/tabbarPages/content'
import { useSubscribeStore } from '@/stores/modules/subscribe'
import { jumpPageTo } from '@/utils/pageTo'

const subscribeStore = useSubscribeStore()
// /**
//  * START：开始
//  * FINISH：结束
//  * VERIFICATE：待核验
//  */
const ACT_STATUS = {
  VERIFICATE: '0',
  START: '1',
  FINISH: '2',
}

// 公告数据（首页展示前二条数据）
const afficheList = ref([])
// 活动数据
const eventList = ref([])

onReady(() => {
  // 公告列表
  const afficheOptions = {
    pageSize: 2,
    pageNum: 1,
    orderByColumn: 1,
    isAsc: 'desc',
  }
  // 活动列表
  const eventOptions = {
    pageSize: 2,
    pageNum: 1,
    orderByColumn: 1,
    isAsc: 'desc',
  }

  getAfficheList(afficheOptions).then((res) => {
    // console.log(res)
    afficheList.value = res.rows
  })

  getEventList(eventOptions).then((res) => {
    eventList.value = res.rows
  })
})
</script>

<template>
  <view class="bg-gradient-to-b from-[#eef2f5] to-[#F8F8F8]">
    <SafeTopArea>
      <SafeArea :tabbar="true">
        <!-- 上拉刷新下拉加载 -->
        <List>
          <scroll-view scroll-y class="flex h-full w-screen flex-col justify-center   pb-[200rpx] font-[PingFangSC] ">
            <image
              alt="背景图片" src="@/static/images/homeIcon/homeBanner.png" mode="aspectFit"
              class="h-[465rpx] w-screen"
            />

            <!-- 内容区 -->
            <view class="z-100 relative top-[-50rpx] flex flex-col gap-[26rpx]">
              <!-- 活动 -->
              <view class="flex w-screen flex-col gap-[26rpx] px-[30rpx] flex-center">
                <view class="flex w-full flex-col gap-[32rpx] rounded-[21rpx] bg-white p-[30rpx]">
                  <view class="flex items-center justify-between">
                    <text class="text-[30rpx] font-[600] leading-[42rpx] text-[#0C0C0C]">活动</text>
                    <view
                      class="text-[25rpx] font-[400] leading-[36rpx] text-[#666666]"
                      @click="jumpPageTo({ url: '/homePages/pages/eventList/index' })"
                    >
                      <text>更多</text>
                      <TnIcon name="right" />
                    </view>
                  </view>
                  <view v-for="(item, index) in eventList" :key="index" class="w-full shrink-0">
                    <view class="relative flex w-full flex-col gap-[20rpx]">
                      <!-- 上半部分 -->
                      <view
                        style="background: url('/static/images/homeIcon/subBannerUp.png') no-repeat; background-size: 100% 100%"
                        class="relative left-0 top-0 m-auto h-[90rpx] w-full"
                      >
                        <text
                          class="absolute left-[85rpx] top-[20rpx] text-[27rpx] font-[600] leading-[38rpx] text-[#FFFFFF]"
                        >
                          {{ item.auditStartTime.slice(0, 10) }}
                        </text>
                      </view>
                      <!-- 下半部分 -->
                      <view
                        style="background: url('/static/images/homeIcon/subBannerDown.png') no-repeat; background-size: 100% 100%"
                        class="relative left-0 top-[-26rpx] m-auto min-h-[250rpx] w-full px-[40rpx] pb-[20rpx]"
                      >
                        <!-- 点击进入活动详情 -->
                        <view @click="jumpPageTo({ url: '/homePages/pages/eventDetail/index' })">
                          <!-- 地点 -->
                          <view>
                            <text class="text-[24rpx] font-[400] leading-[44rpx] text-[#A1A1A1]">
                              <!-- 地点固定 -->
                              {{ eventLocation }}
                            </text>
                          </view>
                          <!-- 标题 -->
                          <view class="pb-[30rpx]">
                            <text class="break-words text-[30rpx] font-[600] leading-[44rpx] text-[#121E36]">
                              {{ item.title }}
                            </text>
                          </view>
                        </view>

                        <!-- 已预约&活动开始 -->
                        <view class="flex flex-col border-t pt-[30rpx]">
                          <view
                            v-if="subscribeStore.isSubscribe || item.inTimeSlot === false"
                            class="flex flex-col gap-[10rpx] text-[25rpx] font-[400] leading-[44rpx] text-[#63676F]"
                          >
                            <view class="flex">
                              <text>考生姓名：<text class="text-[#424346]">韩梅梅</text></text>
                            </view>
                            <view class="flex">
                              <text>审核场次：<text class="text-[#424346]">2025-03-11 09:00-10:00</text></text>
                            </view>
                            <view class="flex">
                              <text>活动说明：<text class="text-[#424346]">请提前10分钟到达技培中心门口，等待进入</text></text>
                            </view>
                            <view class="flex items-center justify-between">
                              <!-- 开始||结束||待核验 -->
                              <view
                                class="rounded-[6rpx] px-[10rpx] py-[3rpx] pb-[5rpx] text-[20rpx] font-[500] leading-[28rpx]"
                                :class="[
                                  item.eventStatus === ACT_STATUS.START
                                    ? 'bg-[#FCE3CF] text-[#F5622D]'
                                    : item.eventStatus === ACT_STATUS.FINISH
                                      ? 'bg-[#EDEDED] text-[#5B5B5B]'
                                      : 'bg-[#CFDDFC] text-[#375EC7]',
                                ]"
                              >
                                {{ item.eventStatus === ACT_STATUS.START ? '已开始' : item.eventStatus
                                  === ACT_STATUS.FINISH
                                  ? '已结束' : '待核验' }}
                              </view>

                              <TnButton
                                v-if="item.eventStatus === ACT_STATUS.START || item.eventStatus === ACT_STATUS.FINISH"
                                plain border-color="#DEDEDE" text-color="#454545"
                                :custom-style="{ borderRadius: '33rpx', padding: '10rpx 23rpx 15rpx 23rpx' }"
                                @click="jumpPageTo({ url: `/auditPages/pages/studentKnow/index?id=${item.id}` })"
                              >
                                考生须知
                              </TnButton>

                              <TnButton
                                v-if="item.eventStatus === ACT_STATUS.VERIFICATE" plain border-color="#DEDEDE"
                                text-color="#454545"
                                :custom-style="{ borderRadius: '33rpx', padding: '10rpx 23rpx 15rpx 23rpx' }"
                                @click="jumpPageTo({ url: `/auditPages/pages/studentKnow/index?id=${item.id}` })"
                              >
                                修改场次
                              </TnButton>
                            </view>
                          </view>

                          <!-- 未预约 -->
                          <view
                            v-if="!subscribeStore.isSubscribe && item.inTimeSlot === true"
                            class="flex justify-between"
                          >
                            <text class="bg-gradient-to-l from-[#FFFFFF] to-[#ECF5FF] px-[20rpx] py-[6rpx]">
                              尚未预约资格审核
                            </text>
                            <TnButton
                              bg-color="#04A889" text-color="#fff" :custom-style="{
                                fontSize: '26rpx',
                                lineHeight: '40rpx',
                                fontWeight: '500',
                                borderRadius: '33rpx',
                                padding: '10rpx 30rpx',
                              }" @click="jumpPageTo({ url: `/homePages/pages/subscribe/index?id=${item.id}` })"
                            >
                              开始预约
                            </TnButton>
                          </view>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
              <!-- 公告 -->
              <view class="flex w-screen flex-col gap-[26rpx] px-[30rpx] flex-center">
                <view class="flex w-full flex-col gap-[32rpx] rounded-[21rpx] bg-white p-[30rpx]">
                  <view class="flex items-center justify-between">
                    <text class="text-[30rpx] font-[600] leading-[42rpx] text-[#0C0C0C]">通知公告</text>
                    <view
                      class="text-[25rpx] font-[400] leading-[36rpx] text-[#666666]"
                      @click="jumpPageTo({ url: '/homePages/pages/afficheList/index' })"
                    >
                      <text>更多</text>
                      <TnIcon name="right" />
                    </view>
                  </view>
                  <view
                    v-for="(item, index) in afficheList" :key="index" class="flex items-center gap-[24rpx]"
                    @click="jumpPageTo({ url: `/homePages/pages/affiche/index?id=${item.id}` })"
                  >
                    <image
                      :src="item.coverUrl ? item.coverUrl : '/static/images/homeIcon/affiche.png'" mode="aspectFit"
                      class=" h-[113rpx] w-[208rpx]" @error="item.coverUrl = '/static/images/homeIcon/affiche.png'"
                    />
                    <view class="line-clamp-3 flex-1 text-[28rpx] font-[400] leading-[40rpx] text-[#383838]">
                      {{ item.title }}
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </scroll-view>
        </List>
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
