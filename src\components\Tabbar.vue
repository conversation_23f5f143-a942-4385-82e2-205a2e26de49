<script setup>
import TnTabbarItem from '@tuniao/tnui-vue3-uniapp/components/tabbar/src/tabbar-item.vue'
import TnTabbar from '@tuniao/tnui-vue3-uniapp/components/tabbar/src/tabbar.vue'
import { specialistTabbarData, tabbarData } from '@/pages/tabbarPages/content.js'
import { useTabbarStore } from '@/stores/modules/tabbar'

const props = defineProps({
  tabbarType: {
    type: String,
  },
})
const emits = defineEmits(['changeSwitchPages'])
const tabbar = props.tabbarType === 'specialist' ? specialistTabbarData : tabbarData

const tabbarStore = useTabbarStore()

function switchPage(item, index) {
  // console.log('change', item)
  emits('changeSwitchPages', item)
  tabbarStore.activeTabbar(index)
}
</script>

<template>
  <TnTabbar v-model="tabbarStore.currentActiveTabbarIndex" fixed frosted switch-animation :z-index="20" height="100rpx">
    <TnTabbarItem
      v-for="(item, index) in tabbar" :key="index" :icon="item.icon" :active-icon="item.activeIcon"
      :text="item.name" @click="switchPage(item, index)"
    />
  </TnTabbar>
</template>

<style scoped lang="scss"></style>
