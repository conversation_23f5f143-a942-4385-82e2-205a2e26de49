<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnPicker from '@tuniao/tnui-vue3-uniapp/components/picker/src/picker.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import Header from '@/components/Header.vue'
import IdCard from '@/components/IdCard.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { auditCardsArr, reasonOptions } from '@/pages/tabbarPages/content.js'
import { debounce, fuzzySearch } from '@/utils/tools.js'

const searchValue = ref('')
const idCards = ref([])// 信息数组
const showPopup = ref(false)// 弹出框是否显示
const reason = ref('')// 说明

const openPicker = ref(false)
const pickerValue = ref()

const NEED_REASON = reasonOptions.length
idCards.value = auditCardsArr

// 防抖模糊搜索
const debouncedSearch = debounce((val) => {
  idCards.value = fuzzySearch(auditCardsArr, val)
}, 500)

function searchStudent(val) {
  debouncedSearch(val)
}

// 显示弹窗手动核验
function auditClick(item) {
  pickerValue.value = item.pickerValue || ''
  reason.value = item.reason || ''
  showPopup.value = true
}

// 取消
function closeVerificationPopup() {
  pickerValue.value = ''
  reason.value = ''
  showPopup.value = false
}

// 确认
function submitVerification() {
  showPopup.value = false
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8] font-[PingFangSC]">
    <SafeTopArea>
      <SafeArea>
        <view class="flex h-full flex-col p-[20rpx]">
          <!-- 标题 -->
          <view class="relative h-[80rpx]">
            <Header title="身份核验" type="black" :hasBack="false" />
          </view>
          <!-- 搜索 -->
          <view class="mt-[10rpx] bg-white">
            <TnInput v-model="searchValue" :border="false" placeholder="请输入姓名/身份证号" @change="searchStudent">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 内容 -->
          <scroll-view scroll-y class="flex-auto py-[20rpx]">
            <List :list="idCards">
              <template #default="{ dataList }">
                <view v-for="(item, index) in dataList" :key="index">
                  <IdCard :idItem="item" @auditClick="auditClick" />
                </view>
              </template>
            </List>
          </scroll-view>
        </view>
      </SafeArea>
    </SafeTopArea>
  </view>
  <!-- 将弹出层移到SafeTopArea外部 -->
  <TnPopup v-model="showPopup" width="90%">
    <view class="flex flex-col gap-[40rpx] p-[40rpx]">
      <view class="text-center text-[40rpx] font-[500]">
        手动核验
      </view>
      <view class="text-center text-[#646566]">
        请选择该考生通过人工身份核验的原因
      </view>

      <view class="flex items-center">
        <text class="mr-[20rpx] w-[100rpx]">原因</text>
        <view class="border border-[#f8f8f8] pl-[20rpx] text-start" @click="openPicker = true">
          {{ reasonOptions[pickerValue - 1]?.label || '请选择原因' }}
        </view>
      </view>
      <view v-if="pickerValue === NEED_REASON" class="flex items-center">
        <text class="mr-[20rpx] w-[100rpx] pl-[-14rpx]">说明</text>
        <TnInput v-model="reason" placeholder="请输入" underline class="flex-auto" />
      </view>
      <view class="gap-[20rpx] flex-center">
        <TnButton type="info" @click="closeVerificationPopup">
          取消
        </TnButton>
        <TnButton type="primary" @click="submitVerification">
          确认
        </TnButton>
      </view>
    </view>
  </TnPopup>
  <TnPicker v-model="pickerValue" v-model:open="openPicker" :data="reasonOptions" />
</template>

<style scoped lang="scss"></style>
