<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnCheckbox from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox.vue'
import TnFormItem from '@tuniao/tnui-vue3-uniapp/components/form/src/form-item.vue'
import TnForm from '@tuniao/tnui-vue3-uniapp/components/form/src/form.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnNotify from '@tuniao/tnui-vue3-uniapp/components/notify/src/notify.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { FORM_RULES } from '@/pages/tabbarPages/commit.js'
import { loginPupText, loginText } from '@/pages/tabbarPages/content.js'
import { useAuthStore } from '@/stores/modules/auth'
import { jumpPageTo } from '@/utils/pageTo'
import { getSmsCode, userLogin } from '../../api/auth'

const emits = defineEmits(['loginSuccess'])

const authStore = useAuthStore()

const notifyRef = ref()
const formStudRef = ref()
const currentTabIndex = ref(0)
const btnTime = ref()
// // const clickCount = ref()
// const captchaWrited = ref(false)
const showPopup = ref(false)
// 表单数据
const formData = ref({
  identity: '',
  phone: '',
  code: '',
  remember: false,
  allow: false,
})

// #ifndef MP-WEIXIN
btnTime.value = Number.parseFloat(localStorage.getItem('btnTime')) || 0
// clickCount.value = Number.parseFloat(localStorage.getItem('clickCount')) || 0
// #endif

// #ifdef MP-WEIXIN
btnTime.value = Number.parseFloat(wx.getStorageSync('btnTime')) || 0
// clickCount.value = Number.parseFloat(wx.getStorageSync('clickCount')) || 0

// #endif

function showWarn() {
  notifyRef.value?.show({
    msg: '校验失败',
    position: 'center',
    type: 'warning',
  })
}

function login() {
  if (formData.value.allow) {
    formStudRef.value?.validate(async (valid) => {
      if (valid) {
        // uni.showToast({
        //   title: '考生提交成功',
        // })
        // 传输数值优化
        authStore.login()
        // const data = {
        //   clientId: '428a8310cd442757ae699df5d894f051',
        //   grantType: 'sms',
        //   tenantId: '000000', // pc传啥 你这个就传啥 应该是这个
        //   idNumber: formData.value.identity, // 身份证
        //   phonenumber: formData.value.phone, // 手机号
        //   smsCode: formData.value.code, // 短信验证码
        // }
        // const res = await userLogin(data)
        // if (res.code === 200) {
        //   formData.value.username = ''
        //   formData.value.password = ''
        //   authStore.login()
        //   // 用户角色数量
        //   if (authStore.userRoles.length > 1) {
        //     return jumpPageTo({ url: '/pages/pageComponents/LoginChoose' })
        //   }
        //   // 有效时间保持状态
        //   const loginData = {
        //     // 单角色时自动选择，多角色时手动选择
        //     loginStatus: currentTabIndex.value,
        //     time: new Date().getTime(),
        //     expire: formData.value.remember ? 1024 * 1024 * 60 * 24 : 1024 * 1024 * 60 * 24 * 7,
        //   }
        //   // #ifndef MP-WEIXIN
        //   localStorage.setItem('loginData', JSON.stringify(loginData))
        //   // #endif

        //   // #ifdef MP-WEIXIN
        //   wx.setStorage({
        //     key: 'loginData',
        //     data: loginData,
        //   })
        //   // #endif
        //   emits('loginSuccess', 0)
        // }
        if (authStore.userRoles.length > 1) {
          return jumpPageTo({ url: '/pages/pageComponents/LoginChoose' })
        }
        emits('loginSuccess', 0)
      }
      else {
        showWarn()
      }
    })
  }
  else {
    showPopup.value = true
  }
}

function allowClick() {
  showPopup.value = false
  formData.value.allow = true
  login()
}

function getCode() {
  // 每次点击隔60秒
  const params = {
    phonenumber: formData.value.phone,
  }
  getSmsCode(params).then((res) => {
    btnTime.value = 60 * 1000
    console.log(res)
  })

  // clickCount.value = (clickCount.value || 0) + 1

  // // #ifndef MP-WEIXIN
  // localStorage.setItem('clickCount', clickCount.value)
  // // #endif
  // // #ifdef MP-WEIXIN
  // wx.setStorage({
  //   key: 'clickCount',
  //   data: clickCount.value,
  // })
  // // #endif
}

watch(btnTime, (newVal, oldVal) => {
  // 如果值没有变化或者已经是0，不需要处理
  if (newVal === oldVal || newVal <= 0) {
    return
  }
  let timer
  // 清除之前的定时器（如果有）
  if (timer) {
    clearInterval(timer)
  }
  // 设置新的定时器
  timer = setTimeout(() => {
    if (btnTime.value > 0) {
      btnTime.value -= 1000

      // #ifndef MP-WEIXIN
      localStorage.setItem('btnTime', btnTime.value)
      // #endif
      // #ifdef MP-WEIXIN
      wx.setStorage({
        key: 'btnTime',
        data: btnTime.value,
      })
      // #endif
    }
    else {
      clearInterval(timer)
    }
  }, 1000)
}, { immediate: true }) // 添加 immediate 选项，确保初始值也触发
</script>

<template>
  <SafeTopArea>
    <view class="w-screen font-[PingFangSC]">
      <view class="loginBg h-[355rpx] w-full pl-[72rpx] pt-[110rpx]">
        <text class="text-[36rpx] font-[600] leading-[60rpx] text-[#333333]">
          {{ loginText }}
        </text>
      </view>
      <view class="relative top-[-40rpx] z-20 rounded-t-[30rpx] bg-white">
        <view class="p-[54rpx] pb-[20rpx]">
          <TnForm
            ref="formStudRef" :model="formData" :rules="FORM_RULES.STUD" class="p-[40rpx]"
            :hide-required-asterisk="true"
          >
            <view class="flex flex-col gap-[40rpx]">
              <TnFormItem label="身份证号" prop="identity" label-position="top">
                <TnInput v-model="formData.identity" placeholder="请输入身份证号" underline clearable />
              </TnFormItem>
              <TnFormItem label="手机号" prop="phone" label-position="top">
                <TnInput v-model="formData.phone" placeholder="请输入手机号" underline clearable />
              </TnFormItem>
              <TnFormItem label="验证码" prop="code" label-position="top">
                <TnInput v-model="formData.code" placeholder="请输入验证码" underline>
                  <template #suffix>
                    <!-- 每天可以自由发送5次，后续发送需通过图形验证码 -->
                    <TnButton
                      text
                      :text-color="btnTime > 0 || !formData.phone ? '#e0e0e0' : '#34BAA3'"
                      :disabled="btnTime > 0 || !formData.phone" @click="getCode"
                    >
                      {{ btnTime > 0 ? `获取验证码(${btnTime / 1000}s)` : '获取验证码' }}
                    </TnButton>
                  </template>
                </TnInput>
              </TnFormItem>
            </view>
          </TnForm>
          <view class="flex-center">
            <TnButton
              type="success" width="90%" height="80rpx" font-size="40rpx" bg-color="#04A889" text-color="#fff"
              @click="login"
            >
              登录
            </TnButton>
          </view>
          <view class="mt-[30rpx] flex-center">
            <TnCheckbox v-model="formData.remember" checked-shape="circle" size="sm" active-color="#04A889">
              <text class="text-[24rpx]">
                记住登录信息
              </text>
            </TnCheckbox>
          </view>
        </view>
        <view class="pt-[80rpx] flex-center">
          <TnCheckbox v-model="formData.allow" checked-shape="circle" size="sm" active-color="#04A889">
            <view class="text-[22rpx] font-[400] leading-[39rpx] flex-center">
              <text>
                已阅读并同意
                <text class="text-[blue]">《资格审核工具用户协议》</text>
                <text class="text-[blue]">《资格审核工具隐私政策》</text>
              </text>
            </view>
          </TnCheckbox>
        </view>
        <TnNotify ref="notifyRef" />
        <TnPopup v-model="showPopup">
          <view class="tn-p-lg flex flex-col gap-[20rpx]">
            <text>
              {{ loginPupText }}
            </text>
            <view class="gap-[30rpx] flex-center">
              <TnButton bg-color="#e0e0e0" text-color="#fff" @click="showPopup = false">
                取消
              </TnButton>
              <TnButton bg-color="#04a889" text-color="#fff" @click="allowClick">
                同意
              </TnButton>
            </view>
          </view>
        </TnPopup>
      </view>
    </view>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.loginBg {
  background: url('@/static/images/loginBg.png') no-repeat;
  background-size: cover;
}
</style>
