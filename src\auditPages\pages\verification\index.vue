<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { jumpPageTo } from '@/utils/pageTo'

const inputValue = ref('')
</script>

<template>
  <view class="bg-gradient-to-b from-[#E9FFFD] to-[#F5F6F8]">
    <SafeTopArea>
      <SafeArea>
        <List>
          <view class="h-full font-[PingFangSC]">
            <view class="relative h-[360rpx] w-full ">
              <Header title="字符验证码" type="black" />
            </view>
            <view class="flex flex-col gap-[92rpx] px-[93rpx] text-center flex-center">
              <text class="text-[36rpx] font-[600] leading-[60rpx]">
                请向现场工作人员咨询获取六位字符验证码并完成填写
              </text>
              <TnInput
                v-model="inputValue" placeholder="请输入" text-align="center" maxlength="6"
                :custom-style="{ borderRadius: '55rpx', border: '3rpx solid #000000', background: '#fff', padding: '38rpx 180rpx' }"
              />
              <TnButton
                font-size="30rpx"
                :custom-style="{ background: 'linear-gradient(180deg, #3CD1B0 0%, #60CEB5 100%)', boxShadow: '0rpx 2rpx 24rpx 0rpx rgba(87, 207, 180, 0.44)', borderRadius: '44rpx', padding: '23rpx 258rpx', fontWeight: '500', lineHeight: '42rpx' }"
                @click="jumpPageTo({ url: '/auditPages/pages/result/index' })"
              >
                确定
              </TnButton>
            </view>
          </view>
        </List>
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
