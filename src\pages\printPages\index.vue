<script setup>
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import dayjs from 'dayjs'
import BottomBtn from '@/components/BottomBtn.vue'
import Header from '@/components/Header.vue'
import IdCard from '@/components/IdCard.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { printCardsArr } from '@/pages/tabbarPages/content.js'
import { debounce, fuzzySearch } from '@/utils/tools.js'

const searchValue = ref('')
const idCards = ref([])// 人员信息数据

idCards.value = printCardsArr

// 防抖模糊搜索
const debouncedSearch = debounce((val) => {
  idCards.value = fuzzySearch(printCardsArr, val)
}, 500)
function searchStudent(val) {
  debouncedSearch(val)
}

// 打印确认
function printClick(item) {
  item.printStatus = 1
  item.transit = dayjs(new Date()).format('YYYY-MM-DD HH:mm:ss')
}

function submitClick() {
  uni.scanCode({
    scanType: 'qrCode',
    success(res) {
      qrcodeRes.value = res.result
    },
  })
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8] font-[PingFangSC]">
    <SafeTopArea>
      <SafeArea bottomBtn="true">
        <view class="flex h-full flex-col p-[20rpx]">
          <!-- 标题 -->
          <view class="relative h-[80rpx]">
            <Header title="准考证打印确认" type="black" :hasBack="false" />
          </view>
          <!-- 搜索 -->
          <view class="mt-[5px] bg-white">
            <TnInput v-model="searchValue" :border="false" placeholder="请输入姓名/身份证号" @change="searchStudent">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 内容 -->
          <scroll-view scroll-y class="flex-auto py-[10px]">
            <List :list="idCards">
              <template #default="{ dataList }">
                <view v-for="(item, index) in dataList" :key="index">
                  <IdCard :idItem="item" @printClick="printClick" />
                </view>
              </template>
            </List>
          </scroll-view>
        </view>
      </SafeArea>
      <BottomBtn title="扫码确认" icon="scan" @subClick="submitClick" />
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
