<script setup>
import { onReady } from '@dcloudio/uni-app'
import TnOverlay from '@tuniao/tnui-vue3-uniapp/components/overlay/src/overlay.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { flowList } from '@/pages/tabbarPages/content.js'

// 后续通过调用对应接口，获取用户现在的步骤
const flowNow = ref(2)
const flowNo = ref([])
const flowOk = ref([])
const flowArr = ref([])
const stepList = ref()
const showOverlay = ref(false)
const showBig = ref(false)
// 步骤号
const ICON_TYPE = {
  VER: 0,
  SIGN: 1,
  AUDIT: 2,
  PRINT: 3,
  OUT: 4,
}

function setFlow() {
  // 整理步骤图标
  flowNo.value = flowList.filter(item =>
    item.id > flowNow.value,
  ).map(item => item.noIcon)

  flowOk.value = flowList.filter(item =>
    item.id < flowNow.value,
  ).map(item => item.okIcon)

  // 目前状态下的图标
  const combinedFlow = flowOk.value.concat(
    flowList[flowNow.value]?.nowIcon,
    flowNo.value,
  )
  flowArr.value = combinedFlow.map(item => `/static/images/flowIcon/${item}.png`)
  stepList.value = flowList.filter(item => item.id < flowList.length - 1)
  // console.log(stepList.value)
  // console.log(import.meta.env.VITE_BASE_URL)
}

onReady(() => {
  setFlow()
})
</script>

<template>
  <view class="bg-[#f5faf5]">
    <SafeTopArea>
      <SafeArea :tabbar="true">
        <scroll-view scroll-y class="h-full bg-gradient-to-b from-[#F5FAF5] to-[#F9FAFF] font-[PingFangSC]">
          <List>
            <view class=" flex flex-col gap-[30rpx] px-[30rpx] py-[90rpx]">
              <!-- 地图 -->
              <view
                class="flowRound flex flex-col gap-[34rpx] bg-gradient-to-b from-[#F3FFFC] to-[#FFFFFF] p-[30rpx] pb-[50rpx]"
              >
                <view class="relative">
                  <view class="relative z-20 bg-transparent">
                    <text class="text-[30rpx] font-[600] leading-[42rpx] text-[#0C0C0C]">流程全览</text>
                  </view>
                  <view
                    class="absolute left-[-10rpx] top-[25rpx] z-10 h-[21rpx] w-[124rpx] bg-gradient-to-l from-[#FFFFFF] to-[#B6ECCD]"
                  />
                </view>
                <view class="relative">
                  <image
                    alt="背景图片" src="@/static/images/flowIcon/flow.png" mode="aspectFit"
                    class="h-[540rpx] w-[645rpx]"
                  />
                  <!-- 核验 -->
                  <image
                    alt="步骤图" :src="flowArr[ICON_TYPE.VER]" mode="aspectFit"
                    class="iconStyle absolute left-[350rpx] top-[505rpx]"
                  />

                  <!-- 承诺 -->
                  <image
                    alt="步骤图" :src="flowArr[ICON_TYPE.SIGN]" mode="aspectFit"
                    class="iconStyle absolute left-[340rpx] top-[340rpx]"
                  />

                  <!-- 审核 -->
                  <image
                    alt="步骤图" :src="flowArr[ICON_TYPE.AUDIT]" mode="aspectFit"
                    class="iconStyle absolute left-[460rpx] top-[180rpx]"
                  />

                  <!-- 打印 -->
                  <image
                    alt="步骤图" :src="flowArr[ICON_TYPE.PRINT]" mode="aspectFit"
                    class="iconStyle absolute left-[300rpx] top-[180rpx]"
                  />

                  <!-- 离开 -->
                  <image
                    alt="步骤图" :src="flowArr[ICON_TYPE.OUT]" mode="aspectFit"
                    class="iconStyle absolute left-[120rpx] top-[500rpx]"
                  />

                  <!-- 放大 -->
                  <image
                    alt="放大" src="/static/images/flowIcon/showBig.png" mode="aspectFit"
                    class="absolute right-0 top-[550rpx] size-[28rpx]" @click="showBig = true, showOverlay = true"
                  />

                  <!-- 提示 -->
                  <view class="font-400 absolute right-[10rpx] top-0 flex flex-col text-[24rpx] leading-[44rpx]">
                    <view class="flex items-center gap-[10rpx]">
                      <image alt="路线" src="/static/images/flowIcon/up.png" mode="aspectFit" class="size-[30rpx]" />
                      <text>考生资格审查路线</text>
                    </view>
                    <view class="flex items-center gap-[10rpx]">
                      <image alt="警戒" src="/static/images/flowIcon/line.png" mode="aspectFit" class="size-[30rpx]" />
                      <text>警戒线</text>
                    </view>
                    <view class="flex items-center gap-[10rpx]">
                      <image alt="当前阶段" src="/static/images/flowIcon/round.png" mode="aspectFit" class="size-[30rpx]" />
                      <text>您当前所处阶段</text>
                    </view>
                  </view>
                </view>
              </view>

              <!-- 步骤条 -->
              <view class="flowRound flex flex-col gap-[34rpx] bg-gradient-to-b from-[#F3FFFC] to-[#FFFFFF] p-[30rpx]">
                <view class="relative">
                  <view class="relative z-20 bg-transparent">
                    <text class="text-[30rpx] font-[600] leading-[42rpx] text-[#0C0C0C]">进度</text>
                  </view>
                  <view
                    class="absolute left-[-10rpx] top-[25rpx] z-10 h-[21rpx] w-[124rpx] bg-gradient-to-l from-[#FFFFFF] to-[#B6ECCD]"
                  />
                </view>
                <view class="p-[20rpx] ">
                  <up-steps :current="flowNow" direction="column">
                    <up-steps-item v-for="(item, index) in stepList" :key="index" :title="item.name">
                      <template #content>
                        <view class="flex justify-between">
                          <text>{{ item.name }}</text>
                          <text
                            v-if="flowOk.includes(item.okIcon)"
                            class="text-[24rpx] font-[500] leading-[40rpx] text-[#AEAEAF]"
                          >
                            2025年4月22日 18:18:00
                          </text>
                        </view>
                        <text>
                          <text
                            v-if="flowOk.includes(item.okIcon)"
                            class="bg-[#EBFFEE] px-[12rpx] py-[3rpx] text-[20rpx] font-[500] leading-[28rpx] text-[#2CBB6D]"
                          >
                            {{ item.name }}通过
                          </text>
                        </text>
                      </template>
                    </up-steps-item>
                  </up-steps>
                </view>
              </view>
            </view>
          </List>
        </scroll-view>
        <image
          v-show="showBig" alt="放大" src="/static/images/flowIcon/bigMap.jpg" mode="aspectFit"
          class="fixed left-[calc(50vw-40vh)] top-[calc(50vh-40vw)] z-[200] h-[80vw] w-[80vh] rotate-90"
        />
        <TnOverlay v-model:show="showOverlay" :opacity="0.4" :z-index="100" @click="showBig = false" />
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss">
.flowRound {
  border-radius: 21rpx 28rpx 21rpx 21rpx;
  border: 2rpx solid #ffffff;
}

.iconStyle {
  height: 66rpx;
  width: 130rpx;
}
</style>
