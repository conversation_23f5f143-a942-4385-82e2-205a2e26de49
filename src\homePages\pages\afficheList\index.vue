<script setup>
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import { getAfficheList } from '@/api/home/<USER>'
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { jumpPageTo } from '@/utils/pageTo'
import { debounce } from '@/utils/tools.js'

const searchValue = ref('')
const afficheList = ref([])// 公告数据
const paging = ref(null)

function queryList(pageNo, pageSize) {
  const options = {
    pageNum: pageNo,
    pageSize,
    orderByColumn: 1,
    isAsc: 'desc',
  }

  // 只有当 title 有值时才添加到 options 中
  if (searchValue.value) {
    options.title = searchValue.value
  }

  getAfficheList(options)
    .then((res) => {
      paging.value.completeByTotal(res.rows, res.total)
    })
    .catch(() => {
      paging.value.complete(false)
    })
}

// 防抖模糊搜索
const debouncedSearch = debounce(() => {
  // 获取title相关公关
  queryList(1, 10)
}, 500)
function searchStudent(val) {
  // 空格不刷新
  if (val.trim()) {
    debouncedSearch()
  }
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8] font-[PingFangSC]">
    <SafeTopArea>
      <SafeArea>
        <view class="flex h-full flex-col p-[20rpx]">
          <!-- 标题 -->
          <view class="relative h-[80rpx]">
            <Header title="公告列表" type="black" />
          </view>
          <!-- 搜索 -->
          <view class="mt-[5px] bg-white">
            <TnInput v-model="searchValue" :border="false" placeholder="请输入公告" @change="searchStudent">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 内容 -->
          <scroll-view scroll-y class="flex-auto py-[10px]">
            <z-paging
              ref="paging" v-model="afficheList" class="h-full" :fixed="false"
              :auto-hide-loading-after-first-loaded="false" height="100%" @query="queryList"
            >
              <view class="flex flex-col gap-[20rpx]">
                <view
                  v-for="(item, index) in afficheList" :key="index"
                  class="flex items-center gap-[24rpx] bg-white p-[20rpx]"
                  @click="jumpPageTo({ url: `/homePages/pages/affiche/index?id=${item.id}` })"
                >
                  <image
                    :src="item.coverUrl ? item.coverUrl : '/static/images/homeIcon/affiche.png'"
                    mode="aspectFit" class=" h-[113rpx] w-[208rpx]" @error="item.coverUrl = '/static/images/homeIcon/affiche.png'"
                  />
                  <view class="line-clamp-3 flex-1 text-[28rpx] font-[400] leading-[40rpx] text-[#383838]">
                    {{ item.title }}
                  </view>
                </view>
              </view>
            </z-paging>
          </scroll-view>
        </view>
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
