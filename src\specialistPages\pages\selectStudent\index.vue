<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnCheckboxGroup from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox-group.vue'
import TnCheckbox from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import TnListItem from '@tuniao/tnui-vue3-uniapp/components/list/src/list-item.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import BottomBtn from '@/components/BottomBtn.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { studentArr } from '@/pages/specialistPages/content.js'
import { useSpecialistStore } from '@/stores/modules/specialist.js'
import { jumpPageBack } from '@/utils/pageTo'
import { debounce, fuzzySearch } from '@/utils/tools.js'

const selectStudValue = ref([])
const bottomTitle = ref('确定')
const studentList = ref([])
const showPopup = ref(false)
const searchValue = ref('')

const specialistStore = useSpecialistStore()

function submitClick() {
  // 提交选中的学生
  // console.log('提交选中的学生:', selectStudValue.value)
  jumpPageBack()
}

// 防抖模糊搜索
const debouncedSearch = debounce((val) => {
  studentList.value = fuzzySearch(studentArr, val)
}, 500)

function searchPerson(val) {
  debouncedSearch(val)
  // console.log('111')
}

watch(() => selectStudValue.value, (newVal) => {
  // 更新底部按钮标题
  bottomTitle.value = newVal.length > 0 ? `确定(${newVal.length}/${studentArr.length})` : '确定'
  // 更新store中的学生选择值
  specialistStore.setStudent(studentArr.filter(item => newVal.includes(item.id)))
  // console.log(specialistStore.student);
})

onReady(() => {
  studentList.value = studentArr
  selectStudValue.value = specialistStore.student || []
})
</script>

<template>
  <SafeTopArea>
    <!-- 正文 -->
    <view class="font-[PingFangSC]">
      <view class="header relative h-[130rpx] w-full bg-white">
        <Header title="选择考生" type="black" @rightClick="submitClick" />
      </view>
      <SafeArea header="true" bottomBtn="true">
        <view class="relative m-auto flex h-full flex-col bg-white px-[20px]">
          <!-- 搜索 -->
          <view class="rounded-[15rpx] bg-[#F5F6F8]">
            <TnInput v-model="searchValue" :border="false" placeholder="搜索" @change="searchPerson">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 选择列表 -->
          <scroll-view scroll-y class="flex-auto overflow-hidden">
            <List :list="studentList">
              <template v-if="studentList.length > 0" #default="{ dataList }">
                <TnCheckboxGroup v-model="selectStudValue" :wrap="true">
                  <TnCheckbox
                    v-for="(item, index) in dataList" :key="index" :label="item.id" checked-shape="circle"
                    :custom-style="{ borderBottom: '1px solid #E6E6E6' }"
                  >
                    <view class="flex gap-[20rpx] flex-center">
                      <view
                        class="size-[77rpx] rounded-[50%] bg-gradient-to-b from-[#76c1e4] to-[#b4e6ee] text-[50rpx] font-[600] text-white flex-center"
                      >
                        {{ item.name[0] }}
                      </view>
                      <text class="text-[30rpx] font-[400] leading-[106rpx]">
                        {{ item.name }}
                      </text>
                    </view>
                  </TnCheckbox>
                </TnCheckboxGroup>
              </template>
            </List>
          </scroll-view>
        </view>
        <BottomBtn :title="bottomTitle" @subClick="submitClick">
          <view class="flex flex-1 items-center gap-[10rpx] px-[20rpx]" @click="showPopup = true">
            <text class="text-[26rpx] font-[400] leading-[37rpx] text-[#171717]">
              已选择({{ selectStudValue.length }}):
            </text>
            <!--  显示前3个被选人员头像 -->
            <view
              v-for="(item, index) in selectStudValue.slice(0, 3)" :key="index"
              class="size-[54rpx] rounded-[50%] bg-gradient-to-b from-[#76c1e4] to-[#b4e6ee] text-[30rpx] font-[600] text-white flex-center"
            >
              {{ studentArr[item].name[0] }}
            </view>
          </view>
        </BottomBtn>
      </SafeArea>
    </view>

    <TnPopup v-model="showPopup" open-direction="bottom" height="90%">
      <!-- 已选择内容 -->
      <view class="flex h-full flex-col">
        <!-- 标题 -->
        <view class="flex items-center justify-between p-[20rpx] text-[30rpx] leading-[42rpx] text-[#171717]">
          <TnIcon name="close" size="46rpx" color="#171717" @click="showPopup = false" />
          <text class="font-[500]">
            已选择({{ selectStudValue.length }}):
          </text>
          <text class="font-[400]" @click="showPopup = false">确定</text>
        </view>
        <scroll-view scroll-y class="flex-auto overflow-hidden">
          <List :list="selectStudValue">
            <template v-if="selectStudValue.length > 0" #default="{ dataList }">
              <TnListItem
                v-for="(item, index) in dataList" :key="index"
                :custom-style="{ backgroundColor: 'transparent', borderBottom: '1px solid #EFEFEF' }"
              >
                <view class="flex items-center justify-between">
                  <!-- 被选人名 -->
                  <view class="flex items-center gap-[37rpx]">
                    <view
                      class="size-[77rpx] rounded-[50%] bg-gradient-to-b from-[#76c1e4] to-[#b4e6ee] text-[50rpx] font-[600] text-white flex-center"
                    >
                      {{ studentArr[item].name[0] }}
                    </view>
                    <text class="text-[30rpx] font-[400] leading-[106rpx]">
                      {{ studentArr[item].name }}
                    </text>
                  </view>
                  <!-- 操作 -->
                  <TnButton
                    plain border-color="#DEDEDE" width="106rpx" height="54rpx"
                    :custom-style="{ borderRadius: '27rpx' }"
                    @click="selectStudValue = selectStudValue.filter(i => i !== item)"
                  >
                    <text class="text-[24rpx] font-[500] leading-[33rpx] text-[#454545]">
                      移除
                    </text>
                  </TnButton>
                </view>
              </TnListItem>
            </template>
          </List>
        </scroll-view>
      </view>
    </TnPopup>
  </SafeTopArea>
</template>

<style scoped lang="scss">
.header {
  box-shadow: 0rpx 2rpx 8rpx 0rpx rgba(200, 199, 199, 0.32);
}
</style>
