<script setup>
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import Header from '@/components/Header.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { sign, studentKnow } from '@/pages/tabbarPages/content'
import { jumpPageTo } from '@/utils/pageTo'
import { debounce } from '@/utils/tools'

const status = ref(3)
// loading显示
const isShow = ref(true)
// 二维码内容
const qrCodeText = ref('https://uqrcode.cn/doc')

// 添加时间戳参数，用于刷新二维码
const timestamp = ref(Date.now())

// 二维码是否过期
const isExpired = ref(false)

// 剩余有效时间（秒）
const remainingTime = ref(30)

const qrCodeImage = ref('')

// 倒计时函数
let countdownTimer = null
// 0-未开始 1-开始 2-阅读签署 3-签署 4-资格审核
const AUDIT_STATUS = {
  NO_START: 0,
  START: 1,
  STUD_KNOW: 2,
  SIGN: 3,
  AUDIT: 4,
}

// 扫码
function scanCode() {
  uni.scanCode({
    // success(res) {
    //   console.log(`条码类型：${res.scanType}`)
    //   console.log(`条码内容：${res.result}`)
    // },
  })
}

function startCountdown() {
  // 清除之前的计时器
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }

  // 设置新的计时器
  countdownTimer = setInterval(() => {
    if (remainingTime.value > 0) {
      remainingTime.value--
    }
    else {
      // 时间到，标记为过期
      isExpired.value = true
      // 清除计时器
      clearInterval(countdownTimer)
    }
  }, 1000)
}

// 刷新二维码
function refreshQRCode() {
  isShow.value = true
  // 更新时间戳
  timestamp.value = Date.now()
  // 重置过期状态
  isExpired.value = false
  // 重置倒计时
  remainingTime.value = 30
  // 开始倒计时
  startCountdown()
  // 二维码图片URL
  // 使用在线二维码生成服务
  // 添加时间戳参数，确保每次刷新都会生成新的URL
  qrCodeImage.value = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(qrCodeText.value)}&t=${timestamp.value}`
}

// 提前生成防抖函数并保存
const debouncedRefreshQRCode = debounce(refreshQRCode, 500)

function handleRefresh() {
  debouncedRefreshQRCode()
}
// 组件挂载时开始倒计时
onReady(() => {
  refreshQRCode()
})

// 组件卸载时清除计时器
onUnload(() => {
  if (countdownTimer) {
    clearInterval(countdownTimer)
  }
})
</script>

<template>
  <view :class="[status === AUDIT_STATUS.NO_START ? 'bg-[#f7f8f8]' : 'bg-gradient-to-b from-[#E9FFFD] to-[#F5F6F8]']">
    <SafeTopArea>
      <SafeArea :tabbar="true">
        <view class="h-full font-[PingFangSC]">
          <List>
            <!-- 未开始 -->
            <view v-if="status === AUDIT_STATUS.NO_START" class="flex h-full w-screen flex-col justify-center">
              <image
                alt="背景图片" src="@/static/images/homeIcon/homeBanner.png" mode="aspectFit"
                class="h-[465rpx] w-screen"
              />
              <view
                class="audRound relative m-auto w-[90%] border-[2rpx] border-white bg-gradient-to-b from-[#E8FFF8] to-[#FFFFFF] px-[55rpx] py-[80rpx]"
              >
                <view
                  style="background: url('/static/images/auditIcon/waitAudit.png') no-repeat ; background-size: contain"
                  class="absolute left-[-10rpx] top-[-5rpx] h-[68rpx] w-[238rpx]"
                />
                <text class="fomnt-[500]  text-[34rpx] leading-[56rpx] text-[#071913]">
                  国网江苏省电力有限公司2025年高校毕业生招聘考试资格审核活动
                </text>
              </view>
            </view>
            <!-- 开始 -->
            <view
              v-if="status === AUDIT_STATUS.START"
              class="flex h-full w-screen flex-col bg-gradient-to-b from-[#E9FFFD] to-[#F5F6F8]"
            >
              <Header title="审核指引" type="black" :hasBack="false" />
              <view class="relative top-[200rpx] flex flex-col gap-[25rpx] px-[73rpx]">
                <text class="text-[38rpx] font-[600] leading-[60rpx] text-[#000000]">
                  资格审核指引
                </text>
                <view class="flex flex-col gap-[12rpx]">
                  <text class="text-[30rpx] font-[400] leading-[40rpx] text-[#9096A2]">
                    请跟随考场工作人员，前往指定地点扫码进行
                  </text>
                  <view>
                    <text class="text-[30rpx] font-[400] leading-[40rpx] text-[#9096A2]">
                      下一流程：
                    </text>
                    <text class="text-[30rpx] font-[400] leading-[40rpx] text-[#04A889]">
                      查看考生须知、签署承诺书
                    </text>
                  </view>
                </view>
                <view class="flex flex-col gap-[44rpx] flex-center">
                  <image src="/static/images/auditIcon/common.png" class="h-[350rpx] w-[531rpx]" />
                  <view
                    class="gap-[20rpx] rounded-[44rpx] bg-gradient-to-b from-[#3CD1B0] to-[#60CEB5] px-[160rpx] py-[23rpx] flex-center"
                    @click="scanCode"
                  >
                    <TnIcon name="scan" size="30rpx" color="#fff" />
                    <text class="text-[30rpx] font-[500] leading-[42rpx] text-white">
                      点击扫描二维码
                    </text>
                  </view>
                  <text
                    class="text-[25rpx] leading-[40rpx] text-[#0C59ED]"
                    @click="jumpPageTo({ url: '/auditPages/pages/verification/index' })"
                  >
                    无法扫描二维码？请点击这里
                  </text>
                </view>
              </view>
            </view>

            <!-- 阅读 / 签署 -->
            <view
              v-if="status === AUDIT_STATUS.STUD_KNOW || status === AUDIT_STATUS.SIGN"
              class="flex h-full w-screen flex-col bg-gradient-to-b from-[#E9FFFD] to-[#F5F6F8]"
            >
              <view class="relative top-[150rpx] flex flex-col gap-[25rpx] px-[73rpx] text-center">
                <text class="text-[38rpx] font-[600] leading-[60rpx] text-[#000000]">
                  {{ status === AUDIT_STATUS.STUD_KNOW ? studentKnow.title : sign.title }}
                </text>
                <view class="flex flex-col gap-[12rpx] text-left">
                  <text class="text-[30rpx] font-[400] leading-[48rpx] text-[#61666F]">
                    {{ status === AUDIT_STATUS.STUD_KNOW ? studentKnow.hint : sign.hint }}
                  </text>
                </view>
                <view class="flex flex-col gap-[44rpx] flex-center">
                  <image src="/static/images/auditIcon/common.png" class="h-[350rpx] w-[531rpx]" />
                  <view
                    class="rounded-[44rpx] bg-gradient-to-b from-[#3CD1B0] to-[#60CEB5] px-[228rpx] py-[23rpx] flex-center"
                    @click="jumpPageTo({ url: `/auditPages/pages/${status === AUDIT_STATUS.STUD_KNOW ? 'studentKnow' : 'commit'}/index` })"
                  >
                    <text class="text-[30rpx] font-[500] leading-[42rpx] text-white">
                      开始阅读
                    </text>
                  </view>
                </view>
              </view>
            </view>

            <!-- 审核指引 -->
            <view
              v-if="status === AUDIT_STATUS.AUDIT"
              class="flex h-full w-screen flex-col bg-gradient-to-b from-[#E9FFFD] to-[#F5F6F8]"
            >
              <Header title="资格审核" type="black" :hasBack="false" />
              <view class="relative top-[150rpx] px-[34rpx] ">
                <view
                  class="flex flex-col gap-[25rpx] rounded-[18rpx] border-[3rpx]  border-white bg-gradient-to-b from-[#F5FFFC] to-[#FFFFFF] px-[57rpx] py-[61rpx]"
                >
                  <text class="text-[38rpx] font-[600] leading-[60rpx]">
                    资格审核指引
                  </text>
                  <text class="text-[32rpx] font-[400] leading-[40rpx] text-[#9096A2]">
                    请前往 <text class="text-[#04A889]">102教室-3号桌</text> 进行资格审核
                  </text>
                  <view class="relative m-auto">
                    <view class="size-[400rpx]">
                      <up-loading-icon
                        :show="isShow" size="50" mode="circle"
                        custom-style="position:absolute;left:50%;top:50%;transform:translate(-50%,-50%)"
                      />
                      <image
                        :src="qrCodeImage" mode="widthFix" :class="[isExpired ? 'opacity-30' : '']"
                        style="width: 400rpx" @load="isShow = false"
                      />
                    </view>
                    <!-- 过期遮罩 -->
                    <view v-if="isExpired" class="absolute inset-0 flex-center">
                      <view class="rounded-lg bg-[#00000080] px-4 py-2">
                        <text class="text-[28rpx] font-[500] text-white">二维码已过期</text>
                      </view>
                    </view>
                  </view>
                  <view class="flex flex-col items-center gap-[10rpx]">
                    <text class="text-center text-[28rpx] font-[400] leading-[40rpx] text-[#6E727B]">
                      请主动向审核人员出示此二维码
                    </text>
                    <text class="text-center text-[24rpx] font-[400] leading-[34rpx] text-[#9096A2]">
                      有效时间：
                      {{ remainingTime }}秒
                    </text>
                    <view
                      class="mt-[20rpx] rounded-[44rpx] bg-gradient-to-b from-[#3CD1B0] to-[#60CEB5] px-[40rpx] py-[15rpx] flex-center"
                      @click="handleRefresh"
                    >
                      <text class="text-[26rpx] font-[500] leading-[36rpx] text-white">刷新二维码</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </List>
        </view>
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style>
.audRound {
  border-radius: 7rpx 70rpx 7rpx 70rpx;
}

:deep(.u-loading-icon) {
  position: absolute;
}
</style>
