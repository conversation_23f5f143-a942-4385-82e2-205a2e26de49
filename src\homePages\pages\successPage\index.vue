<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { jumpPageTo } from '@/utils/pageTo'

function backHome() {
  jumpPageTo({ url: '/pages/tabbarPages/index' })
}
</script>

<template>
  <SafeTopArea class="flex flex-col bg-[#fafafa] font-[PingFangSC]">
    <view class="relative h-[65px] w-full bg-white">
      <Header type="black" />
    </view>
    <SafeArea header="true">
      <view class="m-auto mt-[30px] flex w-[95%] flex-col gap-[40px] rounded-[5px] bg-white py-[30px] flex-center">
        <view class="flex flex-col gap-[10px] flex-center">
          <image src="/static/images/homeIcon/success.png" class="h-[120px] w-[175px]" />
          <text class="text-[15px] font-[500] leading-[22.5px] text-[#202027]">
            预约成功
          </text>
        </view>
        <TnButton plain border-color="#C3C3C3" width="90%" height="40px" custom-class="myBtn" @click="backHome">
          返回首页
        </TnButton>
      </view>
    </SafeArea>
  </SafeTopArea>
</template>

<style>
  .myBtn {
  border-radius: 20px;
}
</style>
