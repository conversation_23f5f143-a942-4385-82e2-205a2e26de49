<script setup>
import SafeTopArea from '@/components/SafeTopArea.vue'

const props = defineProps({
  // 数据列表
  list: {
    type: Array,
  },
  // 是否固定高度
  fixed: {
    type: <PERSON>olean,
    default: false,
  },
})

const dataList = ref([])
const paging = ref(null)

function queryList(pageNo, pageSize) {
  setTimeout(() => {
    // 计算当前页的数据范围
    const startIndex = (pageNo - 1) * pageSize
    const endIndex = pageNo * pageSize

    // 获取当前页的数据切片
    const currentPageData = props.list.slice(startIndex, endIndex)

    // 如果当前页没有数据，说明已经加载完了
    if (currentPageData.length === 0) {
      paging.value.complete(true)
    }
    else {
      paging.value.complete(currentPageData)
    }
  }, 500)
}

// 下拉刷新被触发
function onRefresh() {
  // 告知z-paging下拉刷新结束，这样才可以开始下一次的下拉刷新
  setTimeout(() => {
    // 1.5秒之后停止刷新动画
    paging.value.complete()
  }, 500)
}

// 监听数组变化，自动刷新列表
watch(() => props.list, () => {
  paging.value?.reload()
})

defineExpose({
  reload: () => {
    paging.value.reload()
  },
})
</script>

<template>
  <!-- 处理数据 -->
  <z-paging
    v-if="props.list" ref="paging" v-model="dataList" :fixed="fixed"
    :auto-hide-loading-after-first-loaded="false" @query="queryList"
  >
    <template #loading>
      <view class="fixed inset-0 flex items-center justify-center">
        <view class="flex flex-col items-center">
          <image src="/static/images/loading.gif" mode="aspectFit" class="mb-5 size-20" />
          <text class="text-base text-gray-600">加载中...</text>
        </view>
      </view>
    </template>
    <slot :dataList="dataList" />
  </z-paging>

  <!-- 处理页面 -->
  <z-paging v-else ref="paging" refresher-only @onRefresh="onRefresh">
    <SafeTopArea>
      <slot />
    </SafeTopArea>
  </z-paging>
</template>

<style scoped></style>
