# 顶层配置文件，停止向上查找
root = true

# 通用配置（所有文件适用）
[*]
charset = utf-8          # 文件编码[1,3,7](@ref)
indent_style = space     # 使用空格缩进[1,3,8](@ref)
indent_size = 2          # 缩进2空格[1,7,8](@ref)
end_of_line = lf         # Unix风格换行符[1,3,7](@ref)
trim_trailing_whitespace = true  # 自动删除行尾空格[1,8](@ref)
insert_final_newline = true      # 文件末尾空行[1,8](@ref)
max_line_length = 120    # 行宽限制[3,7](@ref)

# 特定文件类型配置
[*.{js,ts,vue}]
quote_type = single      # 单引号（需配合ESLint生效）[7](@ref)

[*.md]
indent_size = 4          # Markdown缩进4空格[3,7](@ref)

[package.json]
indent_size = 2          # JSON文件保持2空格缩进[7](@ref)

[*.{html,css,scss}]
indent_size = 2          # HTML/CSS相关文件统一缩进[7,8](@ref)

[*.yml]
indent_size = 2          # YAML文件缩进[3](@ref)