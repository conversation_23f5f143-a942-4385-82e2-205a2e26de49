<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnInput from '@tuniao/tnui-vue3-uniapp/components/input/src/input.vue'
import { getEventList } from '@/api/home/<USER>'
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { eventLocation } from '@/pages/tabbarPages/content'
import { useSubscribeStore } from '@/stores/modules/subscribe'
import { jumpPageTo } from '@/utils/pageTo'
import { debounce } from '@/utils/tools.js'

const subscribeStore = useSubscribeStore()
const searchValue = ref('')
const eventList = ref([])// 活动数据
const paging = ref(null)

// /**
//  * START：开始
//  * FINISH：结束
//  * VERIFICATE：待核验
//  */
const ACT_STATUS = {
  VERIFICATE: '0',
  START: '1',
  FINISH: '2',
}
function queryList(pageNo, pageSize) {
  const options = {
    pageNum: pageNo,
    pageSize,
    orderByColumn: 1,
    isAsc: 'desc',
  }

  // 只有当 title 有值时才添加到 options 中
  if (searchValue.value) {
    options.title = searchValue.value
  }

  getEventList(options)
    .then((res) => {
      paging.value.completeByTotal(res.rows, res.total)
    })
    .catch(() => {
      paging.value.complete(false)
    })
}

// 防抖模糊搜索
const debouncedSearch = debounce(() => {
  // 获取title相关公关
  queryList(1, 10)
}, 500)
function searchStudent(val) {
  // 空格不刷新
  if (val.trim()) {
    debouncedSearch()
  }
}
</script>

<template>
  <view class="bg-gradient-to-b from-[rgba(105,210,200,0.13)] to-[#F5F6F8] font-[PingFangSC]">
    <SafeTopArea>
      <SafeArea>
        <view class="flex h-full flex-col p-[20rpx]">
          <!-- 标题 -->
          <view class="relative h-[80rpx]">
            <Header title="活动列表" type="black" />
          </view>
          <!-- 搜索 -->
          <view class="mt-[5px] bg-white">
            <TnInput v-model="searchValue" :border="false" placeholder="请输入活动" @change="searchStudent">
              <template #prefix>
                <TnIcon name="search" />
              </template>
            </TnInput>
          </view>
          <!-- 内容 -->
          <scroll-view scroll-y class="flex-auto py-[10px]">
            <z-paging
              ref="paging" v-model="eventList" class="h-full" :fixed="false"
              :auto-hide-loading-after-first-loaded="false" height="100%" @query="queryList"
            >
              <view class="flex flex-col gap-[20rpx]">
                <view v-for="(item, index) in eventList" :key="index" class="w-full shrink-0">
                  <view class="relative flex w-full flex-col gap-[20rpx]">
                    <!-- 上半部分 -->
                    <view
                      style="background: url('/static/images/homeIcon/subBannerUp.png') no-repeat; background-size: 100% 100%"
                      class="relative left-0 top-0 m-auto h-[90rpx] w-full"
                    >
                      <text
                        class="absolute left-[100rpx] top-[20rpx] text-[27rpx] font-[600] leading-[38rpx] text-[#FFFFFF]"
                      >
                        {{ item.auditStartTime.slice(0, 10) }}
                      </text>
                    </view>
                    <!-- 下半部分 -->
                    <view
                      style="background: url('/static/images/homeIcon/subBannerDown.png') no-repeat; background-size: 100% 100%"
                      class="relative left-0 top-[-26rpx] m-auto min-h-[250rpx] w-full px-[40rpx] pb-[20rpx]"
                    >
                      <!-- 点击进入活动详情 -->
                      <view @click="jumpPageTo({ url: `/homePages/pages/eventDetail/index?id=${item.id}` })">
                        <!-- 地点 -->
                        <view>
                          <text class="text-[24rpx] font-[400] leading-[44rpx] text-[#A1A1A1]">
                            <!-- 地点固定 -->
                            {{ eventLocation }}
                          </text>
                        </view>
                        <!-- 标题 -->
                        <view class="pb-[30rpx]">
                          <text class="break-words text-[30rpx] font-[600] leading-[44rpx] text-[#121E36]">
                            {{ item.title }}
                          </text>
                        </view>
                      </view>

                      <!-- 已预约&活动开始 -->
                      <view class="flex flex-col border-t pt-[30rpx]">
                        <view
                          v-if="subscribeStore.isSubscribe || item.inTimeSlot === false"
                          class="flex flex-col gap-[10rpx] text-[25rpx] font-[400] leading-[44rpx] text-[#63676F]"
                        >
                          <view class="flex">
                            <text>考生姓名：<text class="text-[#424346]">韩梅梅</text></text>
                          </view>
                          <view class="flex">
                            <text>审核场次：<text class="text-[#424346]">2025-03-11 09:00-10:00</text></text>
                          </view>
                          <view class="flex">
                            <text>活动说明：<text class="text-[#424346]">请提前10分钟到达技培中心门口，等待进入</text></text>
                          </view>
                          <view class="flex items-center justify-between">
                            <!-- 开始||结束||待核验 -->
                            <view
                              class="rounded-[6rpx] px-[10rpx] py-[3rpx] pb-[5rpx] text-[20rpx] font-[500] leading-[28rpx]"
                              :class="[
                                item.eventStatus === ACT_STATUS.START
                                  ? 'bg-[#FCE3CF] text-[#F5622D]'
                                  : item.eventStatus === ACT_STATUS.FINISH
                                    ? 'bg-[#EDEDED] text-[#5B5B5B]'
                                    : 'bg-[#CFDDFC] text-[#375EC7]',
                              ]"
                            >
                              {{ item.eventStatus === ACT_STATUS.START ? '已开始' : item.eventStatus
                                === ACT_STATUS.FINISH
                                ? '已结束' : '待核验' }}
                            </view>

                            <TnButton
                              v-if="item.eventStatus === ACT_STATUS.START || item.eventStatus === ACT_STATUS.FINISH"
                              plain border-color="#DEDEDE" text-color="#454545"
                              :custom-style="{ borderRadius: '33rpx', padding: '10rpx 23rpx 15rpx 23rpx' }"
                              @click="jumpPageTo({ url: '/auditPages/pages/studentKnow/index' })"
                            >
                              考生须知
                            </TnButton>

                            <TnButton
                              v-if="item.eventStatus === ACT_STATUS.VERIFICATE" plain border-color="#DEDEDE"
                              text-color="#454545"
                              :custom-style="{ borderRadius: '33rpx', padding: '10rpx 23rpx 15rpx 23rpx' }"
                              @click="jumpPageTo({ url: '/homePages/pages/studentKnow/index' })"
                            >
                              修改场次
                            </TnButton>
                          </view>
                        </view>

                        <!-- 未预约 -->
                        <view
                          v-if="!subscribeStore.isSubscribe && item.inTimeSlot === true"
                          class="flex justify-between"
                        >
                          <text class="bg-gradient-to-l from-[#FFFFFF] to-[#ECF5FF] px-[20rpx] py-[6rpx]">
                            尚未预约资格审核
                          </text>
                          <TnButton
                            bg-color="#04A889" text-color="#fff" :custom-style="{
                              fontSize: '26rpx',
                              lineHeight: '40rpx',
                              fontWeight: '500',
                              borderRadius: '33rpx',
                              padding: '10rpx 30rpx',
                            }" @click="jumpPageTo({ url: `/homePages/pages/subscribe/index?id=${item.id}` })"
                          >
                            开始预约
                          </TnButton>
                        </view>
                      </view>
                    </view>
                  </view>
                </view>
              </view>
            </z-paging>
          </scroll-view>
        </view>
      </SafeArea>
    </SafeTopArea>
  </view>
</template>

<style scoped lang="scss"></style>
