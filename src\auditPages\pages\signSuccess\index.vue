<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { useTabbarStore } from '@/stores/modules/tabbar'
import { jumpPageTo } from '@/utils/pageTo'

const tabbarStore = useTabbarStore()
function goBack() {
  tabbarStore.activeTabbar(0)
  jumpPageTo({ url: '/pages/tabbarPages/index' })
}
</script>

<template>
  <SafeTopArea class="flex flex-col bg-[#fafafa] font-[PingFangSC]">
    <view class="relative h-[130rpx] w-full bg-white">
      <Header type="black" />
    </view>
    <SafeArea header="true">
      <view class="m-auto mt-[60rpx] flex w-[95%] flex-col gap-[80rpx] rounded-[10rpx] bg-white py-[60rpx] flex-center">
        <view class="flex flex-col gap-[20rpx] flex-center">
          <image src="/static/images/homeIcon/success.png" class="h-[240rpx] w-[350rpx]" />
          <text class="text-[30rpx] font-[500] leading-[45rpx] text-[#202027]">
            已完成签署
          </text>
        </view>
        <TnButton plain border-color="#C3C3C3" width="90%" height="80rpx" custom-class="myBtn" @click="goBack">
          返回
        </TnButton>
      </view>
    </SafeArea>
  </SafeTopArea>
</template>

<style>
.myBtn {
  border-radius: 20px;
}
</style>
