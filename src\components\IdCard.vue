<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'

const props = defineProps({
  idItem: {
    type: Object,
  },
})
const emit = defineEmits(['printClick', 'auditClick'])

const STATE = {
  NOT_PASS: -1,
  NEED_PASS: 0,
  PASS: 1,
}

function handlePrintClick() {
  emit('printClick', props.idItem)
};

function handleAuditClick() {
  emit('auditClick', props.idItem)
}
</script>

<template>
  <view class="my-[20rpx] rounded-[10rpx] bg-[#FFFFFF] p-[20rpx] ">
    <view class="mb-[20px] flex items-center justify-between font-[500]">
      <view class="flex gap-[20rpx] text-[28rpx] leading-[40rpx] text-[#383838]">
        <text class="font-[500]">{{ props.idItem.name }}</text>
        <text>{{ props.idItem.sex ? "男" : "女" }}</text>
      </view>
      <view class="text-[20rpx] leading-[28rpx]">
        <text
          v-if="props.idItem.status !== undefined" class="rounded-[6rpx] p-[5rpx] px-[13rpx]"
          :class="[props.idItem.status ? props.idItem.status === STATE.NOT_PASS ? 'bg-[#FFE8E8] text-[#E56D6D]' : 'bg-[#EBFFEE] text-[#2CBB6D]' : 'bg-[#EBF1FF] text-[#5377DA]']"
        >
          {{ props.idItem.status ? props.idItem.status === STATE.NOT_PASS ? '未通过' : " 已通过" : "未核验" }}
        </text>

        <text
          v-if="props.idItem.printStatus !== undefined" class="rounded-[6rpx] p-[5px] px-[10px]"
          :class="[props.idItem.printStatus ? 'bg-[#EBFFEE] text-[#2CBB6D]' : 'bg-[#EDEDED] text-[#5D5D5D]']"
        >
          {{ props.idItem.printStatus ? " 已打印" : "未打印" }}
        </text>
      </view>
    </view>
    <view class="fomt-[400] flex flex-col gap-[5px] text-[25rpx] leading-[46rpx]">
      <text>
        身份证号：
        <text class="text-[#424346]">{{ props.idItem.identity }}</text>
      </text>
      <text v-if="props.idItem.session !== undefined">
        预约场次：
        <text class="text-[#424346]">{{ props.idItem.session }}</text>
      </text>
      <text v-if="props.idItem.phone !== undefined">
        手机号码：
        <text class="text-[#424346]">
          {{ props.idItem.phone }}
        </text>
      </text>
      <text v-if="props.idItem.status === STATE.PASS">
        核验通过时间：
        <text class="text-[#424346]">
          {{ props.idItem.transit }}
        </text>
      </text>
      <text v-if="props.idItem.printStatus === STATE.PASS">
        打印确认时间：
        <text class="text-[#424346]">
          {{ props.idItem.transit }}
        </text>
      </text>
    </view>
    <view class="text-right">
      <view v-if="props.idItem.status !== undefined">
        <TnButton
          v-if="props.idItem.status === STATE.NOT_PASS" plain text-color="#454545" border-color="#DEDEDE"
          :custom-style="{ borderRadius: '27rpx' }" @click="handleAuditClick"
        >
          <text class="text-[24rpx] leading-[33rpx]">
            手动核验
          </text>
        </TnButton>
      </view>
      <view v-if="props.idItem.printStatus !== undefined">
        <TnButton
          v-if="props.idItem.printStatus === STATE.NEED_PASS" plain text-color="#00C595" border-color="#00C595"
          :custom-style="{ borderRadius: '27rpx' }" @click="handlePrintClick"
        >
          <text class="text-[24rpx] leading-[33rpx]">
            确认打印
          </text>
        </TnButton>
      </view>
    </view>
  </view>
</template>

<style scoped lang="scss"></style>
