<script setup>
import { onLoad } from '@dcloudio/uni-app'
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnSignBoard from 'tnuiv3p-tn-sign-board/index.vue'
import { ref } from 'vue'
import { getEventMineStatus } from '@/api/home/<USER>'
import { useSignBoardStore } from '@/stores/modules/signBoard'
import { jumpPageBack, jumpPageTo } from '@/utils/pageTo'

const signStore = useSignBoardStore()
const signBoardRef = ref()
const eventId = ref()

const canvasWidth = ref(0)
const canvasHeight = ref(0)

const name = '王小五' // 可根据实际替换成动态名字
onLoad((option) => {
  eventId.value = Number(option.id)
  getEventMineStatus({
    eventId: eventId.value,
  }).then((res) => {
    console.log(res)
  })
})
function saveSign() {
  signBoardRef.value?.save().then((res) => {
    signStore.setImg(res)
    // 图片转base64
    uni.getFileSystemManager().readFile({
      filePath: res, // 选择图片返回的相对路径
      encoding: 'base64', // 编码格式
      success: (data) => {
        console.log(`data:image/${'png'.toLocaleLowerCase()};base64,${data.data}`)
        jumpPageTo({ url: `/auditPages/pages/signSuccess/index?id=${eventId.value}` })
      },
      fail: err => console.log(err.errMsg),
    })
  })
}

function drawCanvas(ctx, charWidth) {
  const wordCount = name.length
  const padding = 0

  const charSize = charWidth * 0.6
  const underlineExtend = charWidth * 0.1
  const underlineYOffset = 0

  ctx.clearRect(0, 0, canvasWidth.value, canvasHeight.value)

  // 绘制每个字的田字格和对角线
  for (let i = 0; i < wordCount; i++) {
    // 设置默认线条样式
    ctx.strokeStyle = '#efefef'
    ctx.lineWidth = 1

    const x = padding + i * charWidth
    const y = 0

    // 十字线：竖线
    ctx.beginPath()
    ctx.moveTo(x + charWidth / 2, y)
    ctx.lineTo(x + charWidth / 2, y + charWidth)
    ctx.stroke()

    // 十字线：横线
    ctx.beginPath()
    ctx.moveTo(x, y + charWidth / 2)
    ctx.lineTo(x + charWidth, y + charWidth / 2)
    ctx.stroke()

    // 对角线：左上 -> 右下
    ctx.beginPath()
    ctx.moveTo(x, y)
    ctx.lineTo(x + charWidth, y + charWidth)
    ctx.stroke()

    // 对角线：右上 -> 左下
    ctx.beginPath()
    ctx.moveTo(x + charWidth, y)
    ctx.lineTo(x, y + charWidth)
    ctx.stroke()

    // 底部横线
    ctx.beginPath()
    ctx.strokeStyle = '#ccc'
    ctx.moveTo(x - underlineExtend, y + charWidth + underlineYOffset)
    ctx.lineTo(x + charWidth + underlineExtend, y + charWidth + underlineYOffset)
    ctx.stroke()
  }

  ctx.font = `${charSize}px FangZhengKaiTi`
  // 绘制文字
  ctx.textAlign = 'center'
  ctx.textBaseline = 'middle'

  for (let i = 0; i < wordCount; i++) {
    const char = name[i]
    const centerX = padding + i * charWidth + charWidth / 2
    const centerYPos = charWidth / 2

    // 浅红填充
    ctx.fillStyle = '#f8bfbf'
    ctx.fillText(char, centerX, centerYPos)
  }
}

onLoad(() => {
  const dpr = uni.getSystemInfoSync().pixelRatio // 获取设备像素比

  uni.getSystemInfo({
    success: (res) => {
      const wordCount = name.length
      const padding = 0
      const charWidth = (res.windowWidth - 2 * padding) / wordCount

      canvasWidth.value = res.windowWidth
      canvasHeight.value = charWidth

      uni.createSelectorQuery()
        .select('#nameCanvas')
        .node(({ node: canvas }) => {
          canvas.width = res.windowWidth * dpr
          canvas.height = charWidth * dpr
          // console.log(canvas);

          const ctx = canvas.getContext('2d')
          ctx.scale(dpr, dpr) // 缩放上下文，提高清晰度

          uni.loadFontFace({
            family: 'FangZhengKaiTi',
            source: 'url("https://basic-1314306493.cos.ap-nanjing.myqcloud.com/%E5%AD%97%E4%BD%93/FangZhengKaiTi.ttf")',
            success: () => {
              drawCanvas(ctx, charWidth)
            },
          })
        })
        .exec()
    },
  })
})
</script>

<template>
  <view class="h-screen w-screen">
    <!-- 签名板和操作按钮 -->

    <!-- 田字格背景 -->
    <view class="container absolute inset-0 z-[-1] flex items-center justify-center">
      <canvas
        id="nameCanvas" class="absolute left-0 top-0 z-[-1]" type="2d"
        :style="{ width: `${canvasWidth}px`, height: `${canvasHeight}px` }"
      />
    </view>
    <view class="absolute left-0 top-0 z-[999] flex h-screen w-screen flex-col">
      <TnSignBoard ref="signBoardRef" width="100%" :height="`${canvasHeight}px`" class="z-[9999]" />
      <view class="flex h-full flex-1 items-center justify-end gap-[10rpx] border-t border-[#D7D7D7] pr-[10rpx]">
        <text class="text-[34rpx] font-[400] leading-[48rpx]">请在签字区中工整规范地书写姓名</text>
        <TnButton type="primary" width="150" :custom-style="{ borderRadius: '34rpx' } " @click="jumpPageBack">
          <text style=" font-family: FangZhengKaiTi, sans-serif;" class="px-[20rpx] text-[28rpx] leading-[40rpx]">
            返回
          </text>
        </TnButton>
        <TnButton
          type="info" bg-color="#fff" :custom-style="{ border: '1px solid #DBDBDB', borderRadius: '34rpx' }"
          @click="signBoardRef?.clear"
        >
          <text class="px-[20rpx] text-[28rpx] leading-[40rpx]">清除重写</text>
        </TnButton>
        <TnButton
          type="success" bg-color="#04A889" text-color="#fff" :custom-style="{ borderRadius: '34rpx' }"
          @click="saveSign"
        >
          <text class="px-[20rpx] text-[28rpx] leading-[40rpx]">提交签字</text>
        </TnButton>
      </view>
    </view>
  </view>
</template>

<style scoped>
.container {
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
</style>
