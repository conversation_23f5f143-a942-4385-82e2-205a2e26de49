<script setup>
import TnButton from '@tuniao/tnui-vue3-uniapp/components/button/src/button.vue'
import TnCheckboxGroup from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox-group.vue'
import TnCheckbox from '@tuniao/tnui-vue3-uniapp/components/checkbox/src/checkbox.vue'
import TnIcon from '@tuniao/tnui-vue3-uniapp/components/icon/src/icon.vue'
import TnPopup from '@tuniao/tnui-vue3-uniapp/components/popup/src/popup.vue'
import TnRadioGroup from '@tuniao/tnui-vue3-uniapp/components/radio/src/radio-group.vue'
import TnRadio from '@tuniao/tnui-vue3-uniapp/components/radio/src/radio.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { handleList, specialistArr, Statistics, studentArr } from '@/pages/specialistPages/content.js'
import { useTabbarStore } from '@/stores/modules/tabbar'
import { jumpPageTo } from '@/utils/pageTo.js'

const emits = defineEmits(['changeSwitchPages'])
const showPopup = ref(false)
const selectStudValue = ref([])
const selectSpecValue = ref('')
const chooseAll = ref(false)
const tabbarStore = useTabbarStore()

const iconArr = handleList.map(item => `/static/images/specialistIcon/${item.icon}.png`)

// 单选判断全选
watch(selectStudValue, (newVal) => {
  if (newVal.length === studentArr.length) {
    chooseAll.value = true
  }
  else {
    chooseAll.value = false
  }
})

// 全选
watch(chooseAll, (newVal) => {
  if (newVal) {
    selectStudValue.value = studentArr.map(item => item.name)
  }
  else {
    // 单选去除一个不导致全部去除
    selectStudValue.value = selectStudValue.value.length === studentArr.length ? [] : selectStudValue.value
  }
})

function switchPage() {
  emits('changeSwitchPages', { key: 'audit' })
  tabbarStore.activeTabbar(1)
}

function cancelClick() {
  showPopup.value = false
  selectStudValue.value = []
  selectSpecValue.value = ''
}

function submitClick() {
  showPopup.value = false
  selectStudValue.value = []
  selectSpecValue.value = ''
}

// 扫描二维码
function scanCustomCode() {
  uni.scanCode({
    scanType: 'qrCode',
    success(res) {
      qrcodeRes.value = res.result
    },
  })
}
</script>

<template>
  <view class="bg-[#eef2f5]">
    <SafeTopArea>
      <SafeArea tabbar="true">
        <List>
          <scroll-view scroll-y class="h-full  font-[PingFangSC]">
            <image
              alt="背景图片" src="@/static/images/homeIcon/homeBanner.png" mode="aspectFit"
              class="h-[465rpx] w-screen"
            />
            <view class="relative top-[-100rpx] flex flex-col  gap-[40rpx] p-[20rpx]">
              <view
                class="specBg flex flex-col px-[70rpx] py-[35rpx] text-[30rpx] font-[500] leading-[42rpx] text-[#323232]"
              >
                <!-- 扫码/暂停/转移 -->
                <view
                  v-for="(item, index) in handleList" :key="index"
                  style="background: url('/static/images/specialistIcon/selectBg.png') no-repeat ; background-size: 100% 100%"
                  class="relative flex py-[46rpx] text-center"
                  @click="index === 0 ? scanCustomCode() : index === 1 ? '' : jumpPageTo({ url: '/specialistPages/pages/shift/index' })"
                >
                  <view class="absolute left-[60rpx]">
                    <TnIcon :name="iconArr[index]" size="40rpx" />
                  </view>
                  <view class="m-auto">
                    {{ item.title }}
                  </view>
                </view>
              </view>

              <!-- 统计 -->
              <view class="statisBg flex flex-col gap-[20px] p-[20px] pb-[40px]">
                <!-- 标题 -->
                <view class="flex items-center justify-between">
                  <view class="relative">
                    <view class="relative z-20 bg-transparent">
                      <text class="text-[30rpx] font-[600] leading-[42rpx] text-[#0C0C0C]">本场统计</text>
                    </view>
                    <view
                      class="absolute left-[-10rpx] top-[25rpx] z-10 h-[21rpx] w-[124rpx] bg-gradient-to-l from-[#FFFFFF] to-[#B6ECCD]"
                    />
                  </view>
                  <view @click="switchPage()">
                    <text class="text-[25rpx] font-[400] leading-[36rpx] text-[#0C59ED]">
                      去审核
                    </text>
                    <TnIcon name="right" size="17rpx" :offset-top="-4" color="#0C59ED" />
                  </view>
                </view>
                <!-- 内容 -->
                <view class="flex justify-center gap-[121rpx] font-[600]">
                  <view v-for="(item, index) in Statistics" :key="index" class="flex flex-col flex-center">
                    <text class="text-[41rpx] leading-[57rpx] text-[#04A889]">
                      {{ item.num }}
                    </text>
                    <text class="text-[26rpx] font-[400] leading-[37rpx] text-[#3A3A3A]">
                      {{ item.title }}
                    </text>
                  </view>
                </view>
              </view>

              <!-- <view>
                <view
                  style="background: url('/static/images/specialistIcon/selectBg.png') no-repeat ; background-size: 100% 100%"
                  class="relative flex py-[46rpx] text-center"
                  @click="jumpPageTo({ url: '/printPages/pages/print/index' })"
                >
                  <view class="m-auto">
                    打印
                  </view>
                </view>
                <view
                  style="background: url('/static/images/specialistIcon/selectBg.png') no-repeat ; background-size: 100% 100%"
                  class="relative flex py-[46rpx] text-center"
                  @click="jumpPageTo({ url: '/verifyPages/pages/verify/index' })"
                >
                  <view class="m-auto">
                    审核
                  </view>
                </view>
              </view> -->
            </view>
          </scroll-view>
        </List>
      </SafeArea>
    </SafeTopArea>

    <!-- 转移 -->
    <TnPopup v-model="showPopup" width="80%">
      <view class="flex flex-col gap-[20px] p-[20px]">
        <view class="border-b border-black pb-[10px]">
          <text class="text-[24px]">转移考生</text>
        </view>
        <view class="grid grid-cols-2 gap-[10px]">
          <view>
            <text class="text-[14px] text-[#90b9ff]">
              考生分配已暂停，请选择需转移学生
            </text>
            <view class="h-[250px] border p-[10px]">
              <scroll-view scroll-y class="max-h-[200px]">
                <TnCheckboxGroup v-model="selectStudValue">
                  <TnCheckbox
                    v-for="(item, index) in studentArr" :key="index" :label="item.name"
                    checked-shape="circle"
                  >
                    <template #left>
                      {{ item.name }}
                    </template>
                  </TnCheckbox>
                </TnCheckboxGroup>
              </scroll-view>
              <view class="flex justify-between pt-[10px]">
                <TnCheckbox v-model="chooseAll" checked-shape="circle">
                  全选
                </TnCheckbox>
                <text class="text-[12px] text-[#afadb0] flex-center">已选择({{ selectStudValue.length }})</text>
              </view>
            </view>
          </view>
          <view>
            <text class="text-[14px] text-[#90b9ff]">
              选择当前场地内的在审专家
            </text>
            <view class="h-[250px] border p-[10px]">
              <scroll-view scroll-y class="max-h-[200px]">
                <TnRadioGroup v-model="selectSpecValue">
                  <TnRadio
                    v-for="(item, index) in specialistArr" :key="index" :label="item.name"
                    checked-shape="circle"
                  >
                    <template #left>
                      {{ item.name }}
                    </template>
                  </TnRadio>
                </TnRadioGroup>
              </scroll-view>
            </view>
          </view>
        </view>
        <view class="gap-[50px] flex-center">
          <TnButton type="success" width="60px" height="35px" bg-color="#d7d7d7" text-color="#fff" @click="cancelClick">
            取消
          </TnButton>
          <TnButton type="success" width="60px" height="35px" bg-color="#00a096" text-color="#fff" @click="submitClick">
            确认
          </TnButton>
        </view>
      </view>
    </TnPopup>
  </view>
</template>

<style scoped>
.specBg {
  background: url('@/static/images/specialistIcon/specHomeBg.png') no-repeat;
  background-size: 100% 100%;
}

.selectBg {
  background: url('@/static/images/specialistIcon/selectBg.png') no-repeat;
  background-size: 100% 100%;
}

.statisBg {
  background: url('@/static/images/specialistIcon/statistics.png') no-repeat;
  background-size: 100% 100%;
}
</style>
