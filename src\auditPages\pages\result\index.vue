<script setup>
import Header from '@/components/Header.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'

const res = ref(1)
const RESULT = {
  FAIL: 0,
  SUCCESS: 1,
}
</script>

<template>
  <SafeTopArea>
    <view class="bg-[#FAFAFA] font-[PingFangSC]">
      <view class="relative h-[130rpx] w-full bg-white">
        <Header title="资格审核" type="black" />
      </view>
      <SafeArea header="true">
        <view class="h-full p-[25rpx]">
          <view class="h-4/5 rounded-[10rpx] bg-white py-[90rpx]">
            <!-- 失败 -->
            <view v-if="res === RESULT.FAIL" class="flex flex-col text-center">
              <image src="/static/images/auditIcon/fail.png" class="mx-auto my-0 h-[241rpx] w-[350rpx]" />
              <text class="text-[30rpx] font-[500] leading-[45rpx] text-[#202027]">审核未通过</text>
            </view>

            <!-- 成功 -->
            <view v-if="res === RESULT.SUCCESS" class="flex flex-col gap-[30rpx] text-center">
              <image src="/static/images/homeIcon/success.png" class="mx-auto my-0 h-[241rpx] w-[350rpx]" />
              <text class="text-[30rpx] font-[500] leading-[45rpx] text-[#202027]">审核通过</text>
              <image src="/static/images/auditIcon/code.png" class="mx-auto my-0 size-[420rpx]" />
              <text class="text-[28rpx] font-[400] leading-[40rpx] text-[#6E727B]">凭此二维码领取准考证</text>
            </view>
          </view>
        </view>
      </SafeArea>
    </view>
  </SafeTopArea>
</template>

<style scoped lang="scss"></style>
