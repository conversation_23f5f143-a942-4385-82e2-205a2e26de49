<script setup>
import TnListItem from '@tuniao/tnui-vue3-uniapp/components/list/src/list-item.vue'
import List from '@/components/List.vue'
import SafeArea from '@/components/SafeArea.vue'
import SafeTopArea from '@/components/SafeTopArea.vue'
import { mineList } from '@/pages/tabbarPages/content.js'
import { useAuthStore } from '@/stores/modules/auth'

onLoad(() => {
})

const authStore = useAuthStore()

function logout() {
  // #ifndef MP-WEIXIN
  localStorage.removeItem('loginData')
  sessionStorage.removeItem('tabbar')

  // #endif
  // #ifdef MP-WEIXIN
  wx.removeStorage({
    key: 'loginData',
    success() {
      wx.removeStorage({
        key: 'tabbar',
      })
    },
  })
  // #endif
  authStore.logout()
}

const username = ref('张三')
</script>

<template>
  <SafeTopArea>
    <List>
      <SafeArea class="bg-white font-[PingFangSC]" :tabbar="true">
        <view class="w-100vw relative  pb-[20px] pl-[20px] pt-[60px]">
          <image src="/static/images/mineIcon/mineBg.png" class="absolute left-0 top-0 w-full " mode="aspectFill" />
          <view class="relative flex items-center gap-[20px]">
            <!-- 头像应由姓名决定 -->
            <!-- <image
            alt="头像" src="/static/images/mineIcon/mineAvatar.png" mode="aspectFill"
            class="size-[110rpx] rounded-[50%]"
          /> -->
            <view
              class="size-[110rpx] rounded-[50%] bg-gradient-to-b from-[#76c1e4] to-[#b4e6ee] text-[50rpx] font-[600] text-white flex-center"
            >
              {{ username[0] }}
            </view>
            <view class="flex flex-col">
              <text class="text-[34rpx] font-[500] leading-[48rpx] text-[#454545]">
                {{ username }}
              </text>
              <text class="text-[26rpx] font-[400] text-[#686868]">
                欢迎使用！
              </text>
            </view>
          </view>
        </view>

        <view
          class="w-100vw relative top-[-10px] m-auto rounded-[10px] bg-gradient-to-b from-[#F6FFFF] to-[#FFFFFF] p-[20px]"
        >
          <TnListItem
            v-for="(item, index) in mineList" :key="index" right-icon="right"
            :custom-style="{ backgroundColor: 'transparent', borderBottom: '1px solid #EFEFEF' }"
            @click="item.id === mineList.length - 1 ? logout() : null"
          >
            {{ item.name }}
          </TnListItem>
        </view>
      </SafeArea>
    </List>
  </SafeTopArea>
</template>

<style scoped lang="scss"></style>
